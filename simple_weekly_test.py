#!/usr/bin/env python
"""
Simple test to check weekly analytics issue
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("🔍 Simple Weekly Analytics Test")
        print("=" * 30)
        
        # Test Firebase connection
        from src.firebase_db import get_data, initialize_firebase
        
        print("1. Testing Firebase connection...")
        if not initialize_firebase():
            print("❌ Firebase failed")
            return
        
        print("✅ Firebase connected")
        
        # Get completed orders
        print("\n2. Getting completed orders...")
        completed_orders = get_data("completed_orders")
        print(f"Completed orders: {len(completed_orders) if completed_orders else 0}")
        
        if completed_orders:
            print("First order sample:")
            first_order = list(completed_orders.items())[0]
            print(f"  ID: {first_order[0]}")
            print(f"  Data: {first_order[1]}")
        
        # Test weekly calculation
        print("\n3. Testing weekly calculation...")
        from web_management import calculate_accurate_analytics_for_period
        
        stats, breakdown = calculate_accurate_analytics_for_period('weekly')
        
        print(f"Results:")
        print(f"  Total orders: {stats.get('total_orders', 0)}")
        print(f"  Date range: {stats.get('start_date')} to {stats.get('end_date')}")
        
        # Check current week
        now = datetime.now()
        current_weekday = now.weekday()
        monday = (now - timedelta(days=current_weekday)).date()
        
        print(f"\nWeek info:")
        print(f"  Today: {now.date()}")
        print(f"  Monday: {monday}")
        print(f"  Expected range: {monday} to {now.date()}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
