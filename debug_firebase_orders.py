#!/usr/bin/env python
"""
Quick debug script to check Firebase order data
"""

import os
import sys
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_firebase_orders():
    """Check what's actually in Firebase"""
    try:
        from src.firebase_db import get_data, initialize_firebase
        
        print("🔥 Checking Firebase Orders...")
        
        # Initialize Firebase
        if not initialize_firebase():
            print("❌ Firebase initialization failed")
            return False
        
        # Get completed orders
        completed_orders = get_data("completed_orders")
        print(f"\nCompleted Orders: {len(completed_orders) if completed_orders else 0}")
        
        if completed_orders:
            for order_id, order_data in completed_orders.items():
                print(f"  {order_id}:")
                print(f"    completion_date: {order_data.get('completion_date', 'N/A')}")
                print(f"    order_date: {order_data.get('order_date', 'N/A')}")
                print(f"    delivery_fee: {order_data.get('delivery_fee', 'N/A')}")
                print(f"    all_keys: {list(order_data.keys())}")
        
        # Get confirmed orders
        confirmed_orders = get_data("confirmed_orders")
        print(f"\nConfirmed Orders: {len(confirmed_orders) if confirmed_orders else 0}")
        
        if confirmed_orders:
            for order_id, order_data in confirmed_orders.items():
                print(f"  {order_id}:")
                print(f"    order_date: {order_data.get('order_date', 'N/A')}")
                print(f"    all_keys: {list(order_data.keys())}")
        
        # Check current week range
        now = datetime.now()
        current_weekday = now.weekday()
        monday_this_week = (now - timedelta(days=current_weekday)).date()
        
        print(f"\nCurrent Week Info:")
        print(f"  Today: {now.date()} ({now.strftime('%A')})")
        print(f"  Monday: {monday_this_week}")
        print(f"  Week range: {monday_this_week} to {now.date()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weekly_calculation():
    """Test the weekly calculation directly"""
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        print("\n🧮 Testing Weekly Calculation...")
        
        stats, daily_breakdown = calculate_accurate_analytics_for_period('weekly')
        
        print(f"Results:")
        print(f"  Total orders: {stats.get('total_orders', 0)}")
        print(f"  Completed: {stats.get('completed_orders', 0)}")
        print(f"  In-progress: {stats.get('in_progress_orders', 0)}")
        print(f"  Pending: {stats.get('pending_orders', 0)}")
        print(f"  Date range: {stats.get('start_date')} to {stats.get('end_date')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Weekly calculation error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🐛 QUICK FIREBASE DEBUG")
    print("=" * 30)
    
    check_firebase_orders()
    test_weekly_calculation()
