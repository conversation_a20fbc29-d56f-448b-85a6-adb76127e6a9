<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Dashboard - Wiz-Aroma</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .debug-section {
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
        }

        .status-success {
            color: #198754;
        }

        .status-error {
            color: #dc3545;
        }

        .status-warning {
            color: #fd7e14;
        }

        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .endpoint-link {
            color: #0d6efd;
            text-decoration: none;
            font-family: monospace;
        }

        .endpoint-link:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-bug"></i> Debug Dashboard</h1>
                    <div>
                        <a href="/analytics" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Back to Analytics
                        </a>
                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="refreshDebugInfo()">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>
                </div>

                {% if error_info %}
                <div class="alert alert-danger">
                    <h5><i class="bi bi-exclamation-triangle"></i> Debug Dashboard Error</h5>
                    <p><strong>Error:</strong> {{ error_info.error }}</p>
                    <details>
                        <summary>Traceback</summary>
                        <pre>{{ error_info.traceback }}</pre>
                    </details>
                </div>
                {% endif %}

                {% if debug_info %}
                <!-- System Information -->
                <div class="debug-section">
                    <h3><i class="bi bi-info-circle"></i> System Information</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Current Date:</strong></td>
                                    <td>{{ debug_info.system_info.current_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Current Time:</strong></td>
                                    <td>{{ debug_info.system_info.current_time }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Weekday:</strong></td>
                                    <td>{{ debug_info.system_info.current_weekday_name }} ({{
                                        debug_info.system_info.current_weekday }})</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Monday This Week:</strong></td>
                                    <td>{{ debug_info.system_info.monday_this_week }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Week Range:</strong></td>
                                    <td>{{ debug_info.system_info.week_range }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Debug Timestamp:</strong></td>
                                    <td>{{ debug_info.timestamp }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Firebase Status -->
                <div class="debug-section">
                    <h3><i class="bi bi-cloud"></i> Firebase Status</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Connection Status:</strong>
                                <span
                                    class="{% if debug_info.firebase_status.connection == 'Connected' %}status-success{% else %}status-error{% endif %}">
                                    {{ debug_info.firebase_status.connection }}
                                </span>
                            </p>
                            {% if debug_info.firebase_status.error %}
                            <p><strong>Error:</strong> <span class="status-error">{{ debug_info.firebase_status.error
                                    }}</span></p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5>Data Counts:</h5>
                            {% if debug_info.firebase_status.data_counts.error %}
                            <p class="status-error">Error: {{ debug_info.firebase_status.data_counts.error }}</p>
                            {% else %}
                            <ul class="list-unstyled">
                                {% for collection, count in debug_info.firebase_status.data_counts.items() %}
                                <li><strong>{{ collection }}:</strong> {{ count }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Analytics Tests -->
                <div class="debug-section">
                    <h3><i class="bi bi-graph-up"></i> Analytics Tests</h3>
                    <div class="row">
                        {% for period, test_result in debug_info.analytics_tests.items() %}
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ period.title() }} Period</h6>
                                </div>
                                <div class="card-body">
                                    {% if test_result.status == 'success' %}
                                    <p class="status-success"><i class="bi bi-check-circle"></i> Success</p>
                                    <small>
                                        <strong>Total Orders:</strong> {{ test_result.total_orders }}<br>
                                        <strong>Completed:</strong> {{ test_result.completed_orders }}<br>
                                        <strong>Earnings:</strong> ${{ "%.2f"|format(test_result.total_earnings) }}<br>
                                        <strong>Date Range:</strong> {{ test_result.date_range }}<br>
                                        <strong>Breakdown Days:</strong> {{ test_result.breakdown_days }}
                                    </small>
                                    {% else %}
                                    <p class="status-error"><i class="bi bi-x-circle"></i> Error</p>
                                    <small class="status-error">{{ test_result.error }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Debug API Endpoints -->
                <div class="debug-section">
                    <h3><i class="bi bi-link-45deg"></i> Debug API Endpoints</h3>
                    <p>Click on any endpoint URL to test it directly:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Analytics Debug APIs:</h5>
                            <ul class="list-unstyled">
                                <li><a href="/api/analytics/debug?period=weekly" target="_blank"
                                        class="endpoint-link">/api/analytics/debug?period=weekly</a></li>
                                <li><a href="/api/analytics/debug?period=daily" target="_blank"
                                        class="endpoint-link">/api/analytics/debug?period=daily</a></li>
                                <li><a href="/api/analytics/debug?period=monthly" target="_blank"
                                        class="endpoint-link">/api/analytics/debug?period=monthly</a></li>
                                <li><a href="/api/analytics/debug?period=all_time" target="_blank"
                                        class="endpoint-link">/api/analytics/debug?period=all_time</a></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Weekly Testing APIs:</h5>
                            <ul class="list-unstyled">
                                <li><a href="/api/analytics/weekly-sync-test" target="_blank"
                                        class="endpoint-link">/api/analytics/weekly-sync-test</a></li>
                                <li><a href="/api/analytics/week-info" target="_blank"
                                        class="endpoint-link">/api/analytics/week-info</a></li>
                                <li><a href="/api/analytics/create-weekly-test-data" target="_blank"
                                        class="endpoint-link">/api/analytics/create-weekly-test-data</a></li>
                                <li><a href="/api/debug/test-all-endpoints" target="_blank"
                                        class="endpoint-link">/api/debug/test-all-endpoints</a></li>
                                <li><a href="/api/debug/run-diagnostics" target="_blank"
                                        class="endpoint-link">/api/debug/run-diagnostics</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="debug-section">
                    <h3><i class="bi bi-lightning"></i> Quick Actions</h3>
                    <div class="row">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-primary w-100 mb-2"
                                onclick="testAllEndpoints()">
                                <i class="bi bi-check2-all"></i> Test All Endpoints
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-warning w-100 mb-2" onclick="runDiagnostics()">
                                <i class="bi bi-search"></i> Run Diagnostics
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-success w-100 mb-2"
                                onclick="createWeeklyTestData()">
                                <i class="bi bi-plus-circle"></i> Create Weekly Test Data
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="openAnalytics()">
                                <i class="bi bi-graph-up"></i> Open Analytics Dashboard
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshDebugInfo() {
            location.reload();
        }

        function testAllEndpoints() {
            fetch('/api/debug/test-all-endpoints')
                .then(response => response.json())
                .then(data => {
                    let message = `Endpoint Test Results:\n\n`;
                    for (const [endpoint, result] of Object.entries(data.results)) {
                        message += `${endpoint}: ${result.status}\n`;
                    }
                    message += `\nCheck browser console for detailed results.`;
                    console.log('Endpoint Test Results:', data);
                    alert(message);
                })
                .catch(error => {
                    console.error('Error testing endpoints:', error);
                    alert('Error testing endpoints: ' + error.message);
                });
        }

        function createWeeklyTestData() {
            if (confirm('Create test orders for the current week?')) {
                fetch('/api/analytics/create-weekly-test-data')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(`Success! Created ${data.created_orders.length} test orders.\n\nRefreshing debug dashboard...`);
                            location.reload();
                        } else {
                            alert('Error: ' + (data.error || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error: ' + error.message);
                    });
            }
        }

        function openAnalytics() {
            window.open('/analytics?period=weekly', '_blank');
        }

        function runDiagnostics() {
            if (confirm('Run comprehensive diagnostics? This will analyze Firebase data, test calculations, and check consistency.')) {
                fetch('/api/debug/run-diagnostics')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Diagnostic Results:', data);

                        let message = 'Diagnostic Results:\n\n';

                        if (data.diagnostics.firebase_analysis) {
                            const fb = data.diagnostics.firebase_analysis;
                            if (fb.status === 'success') {
                                message += `Firebase Analysis: ✅\n`;
                                message += `- Completed Orders: ${fb.completed_orders_count}\n`;
                                message += `- Orders in Current Week: ${fb.current_week_analysis.orders_in_current_week}\n`;
                                message += `- Date Range: ${fb.date_range_in_data.earliest} to ${fb.date_range_in_data.latest}\n\n`;
                            } else {
                                message += `Firebase Analysis: ❌ ${fb.error}\n\n`;
                            }
                        }

                        if (data.diagnostics.weekly_calculation) {
                            const wc = data.diagnostics.weekly_calculation;
                            if (wc.status === 'success') {
                                message += `Weekly Calculation: ✅\n`;
                                message += `- Total Orders: ${wc.results.total_orders}\n`;
                                message += `- Completed Orders: ${wc.results.completed_orders}\n`;
                                message += `- Total Earnings: $${wc.results.total_earnings}\n\n`;
                            } else {
                                message += `Weekly Calculation: ❌ ${wc.error}\n\n`;
                            }
                        }

                        if (data.diagnostics.period_consistency) {
                            const pc = data.diagnostics.period_consistency;
                            if (pc.status === 'success') {
                                message += `Period Consistency: ${pc.all_consistent ? '✅' : '❌'}\n`;
                                message += `- Daily: ${pc.period_results.daily.total_orders} orders\n`;
                                message += `- Weekly: ${pc.period_results.weekly.total_orders} orders\n`;
                                message += `- Monthly: ${pc.period_results.monthly.total_orders} orders\n`;
                                message += `- All Time: ${pc.period_results.all_time.total_orders} orders\n`;
                            } else {
                                message += `Period Consistency: ❌ ${pc.error}\n`;
                            }
                        }

                        message += '\nSee browser console for detailed results.';
                        alert(message);
                    })
                    .catch(error => {
                        console.error('Error running diagnostics:', error);
                        alert('Error running diagnostics: ' + error.message);
                    });
            }
        }
    </script>
</body>

</html>