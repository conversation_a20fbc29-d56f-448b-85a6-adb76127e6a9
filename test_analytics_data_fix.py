#!/usr/bin/env python
"""
Test script to debug and verify analytics data calculation
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, delete_data, initialize_firebase
from src.config import logger

def test_firebase_data_retrieval():
    """Test Firebase data retrieval"""
    print("🔥 Testing Firebase Data Retrieval...")
    
    try:
        from web_management import get_real_time_firebase_data
        
        firebase_data = get_real_time_firebase_data()
        
        if firebase_data:
            print("✅ Firebase data retrieved successfully")
            for collection, data in firebase_data.items():
                print(f"   {collection}: {len(data)} records")
                
                # Show sample data for debugging
                if data and len(data) > 0:
                    sample_id = list(data.keys())[0]
                    sample_data = data[sample_id]
                    print(f"     Sample {sample_id}: {sample_data}")
            
            return firebase_data
        else:
            print("❌ Failed to retrieve Firebase data")
            return None
            
    except Exception as e:
        print(f"❌ Firebase data retrieval failed: {e}")
        return None

def test_analytics_calculation():
    """Test analytics calculation for all periods"""
    print("\n📊 Testing Analytics Calculation...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        periods = ['daily', 'weekly', 'monthly', 'all_time']
        
        for period in periods:
            print(f"\n--- {period.upper()} ANALYTICS ---")
            stats, daily_breakdown = calculate_accurate_analytics_for_period(period)
            
            print(f"Period: {stats.get('period', 'unknown')}")
            print(f"Total Orders: {stats.get('total_orders', 0)}")
            print(f"Completed Orders: {stats.get('completed_orders', 0)}")
            print(f"In Progress Orders: {stats.get('in_progress_orders', 0)}")
            print(f"Pending Orders: {stats.get('pending_orders', 0)}")
            print(f"Completion Rate: {stats.get('completion_rate', 0)}%")
            print(f"Total Earnings: {stats.get('total_earnings', 0):.2f} Birr")
            
            if stats.get('start_date') and stats.get('end_date'):
                print(f"Date Range: {stats['start_date']} to {stats['end_date']}")
            
            print(f"Daily Breakdown Days: {len(daily_breakdown) if daily_breakdown else 0}")
            
            # Check if we have any data
            if stats.get('total_orders', 0) > 0:
                print("✅ Analytics calculation working - found orders")
            else:
                print("⚠️  Analytics calculation returns zero orders")
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics calculation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_date_filtering():
    """Test date filtering logic"""
    print("\n📅 Testing Date Filtering Logic...")
    
    try:
        # Get Firebase data directly
        firebase_data = get_data("completed_orders") or {}
        
        print(f"Total completed orders in Firebase: {len(firebase_data)}")
        
        # Test date parsing
        today = datetime.now().date()
        today_str = today.isoformat()
        
        print(f"Today's date: {today_str}")
        
        today_count = 0
        all_time_count = 0
        
        for order_id, order_data in firebase_data.items():
            all_time_count += 1
            
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
            print(f"Order {order_id}: date='{order_date_str}'")
            
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()
                    
                    print(f"  Parsed date: {order_date}")
                    
                    if order_date == today:
                        today_count += 1
                        print(f"  ✅ Matches today")
                    else:
                        print(f"  ❌ Does not match today")
                        
                except Exception as e:
                    print(f"  ❌ Date parsing failed: {e}")
            else:
                print(f"  ⚠️  No date information")
        
        print(f"\nSummary:")
        print(f"All-time orders: {all_time_count}")
        print(f"Today's orders: {today_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Date filtering test failed: {e}")
        return False

def test_analytics_route():
    """Test analytics route"""
    print("\n🌐 Testing Analytics Route...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            # Test analytics debug endpoint
            response = client.get('/api/analytics/debug?period=all_time')
            
            if response.status_code == 302:  # Redirect to login
                print("✅ Analytics debug endpoint exists (redirects to login)")
                
                # Test with app context
                with app.test_request_context():
                    from web_management import api_analytics_debug
                    
                    # Mock request args
                    import flask
                    with app.test_request_context('/?period=all_time'):
                        try:
                            result = api_analytics_debug()
                            print("✅ Analytics debug function works")
                        except Exception as e:
                            print(f"❌ Analytics debug function failed: {e}")
                
            else:
                print(f"❌ Analytics debug endpoint failed: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics route test failed: {e}")
        return False

def test_sample_data_creation():
    """Create sample data for testing if none exists"""
    print("\n🔧 Testing Sample Data Creation...")
    
    try:
        # Check if we have any data
        completed_orders = get_data("completed_orders") or {}
        
        if len(completed_orders) == 0:
            print("No completed orders found. Creating sample data...")
            
            # Create sample completed order
            today = datetime.now()
            sample_order = {
                'order_id': 'test_order_001',
                'completion_date': today.isoformat(),
                'order_date': today.isoformat(),
                'delivery_fee': 50.0,
                'customer_id': 'test_customer',
                'status': 'completed'
            }
            
            # Save sample order
            set_data(f"completed_orders/test_order_001", sample_order)
            print("✅ Sample completed order created")
            
            # Create sample confirmed order
            sample_confirmed = {
                'order_id': 'test_order_002',
                'order_date': today.isoformat(),
                'customer_id': 'test_customer_2',
                'status': 'confirmed'
            }
            
            set_data(f"confirmed_orders/test_order_002", sample_confirmed)
            print("✅ Sample confirmed order created")
            
            return True
        else:
            print(f"Found {len(completed_orders)} existing completed orders")
            return True
            
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        return False

def main():
    """Run all analytics data fix tests"""
    print("🧪 ANALYTICS DATA FIX VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("Firebase Data Retrieval", test_firebase_data_retrieval),
        ("Sample Data Creation", test_sample_data_creation),
        ("Date Filtering Logic", test_date_filtering),
        ("Analytics Calculation", test_analytics_calculation),
        ("Analytics Route", test_analytics_route)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL ANALYTICS DATA FIX TESTS PASSED!")
        print("\n✅ Next Steps:")
        print("1. Restart the web interface: python web_management.py")
        print("2. Access analytics: http://localhost:5000/analytics")
        print("3. Check debug endpoint: http://localhost:5000/api/analytics/debug?period=all_time")
        print("4. Verify all time periods show correct data")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("❌ Analytics data fix needs attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}: Analytics data fix {'completed' if success else 'needs work'}")
    sys.exit(0 if success else 1)
