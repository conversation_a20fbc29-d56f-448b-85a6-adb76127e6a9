#!/usr/bin/env python
"""
Create test orders for the current week to test weekly analytics
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_current_week_orders():
    """Create orders for the current week"""
    try:
        from src.firebase_db import set_data, initialize_firebase
        
        print("📅 Creating Current Week Test Orders...")
        
        # Initialize Firebase
        if not initialize_firebase():
            print("❌ Firebase initialization failed")
            return False
        
        now = datetime.now()
        current_weekday = now.weekday()  # Monday = 0
        monday_this_week = now - timedelta(days=current_weekday)
        
        print(f"Current week info:")
        print(f"  Today: {now.date()} ({now.strftime('%A')})")
        print(f"  Monday: {monday_this_week.date()}")
        print(f"  Week range: {monday_this_week.date()} to {now.date()}")
        
        # Create orders for each day from Monday to today
        orders_created = []
        
        for i in range(current_weekday + 1):  # From Monday to today
            order_date = monday_this_week + timedelta(days=i)
            day_name = order_date.strftime('%A')
            
            # Create completed order
            completed_order = {
                'order_id': f'test_week_completed_{i}',
                'completion_date': order_date.isoformat(),
                'order_date': order_date.isoformat(),
                'delivery_fee': 50.0 + (i * 10),
                'customer_id': f'test_customer_{i}',
                'status': 'completed',
                'restaurant_name': 'Test Restaurant',
                'delivery_address': f'Test Address {i}',
                'created_by': 'test_script'
            }
            
            order_key = f"test_week_completed_{i}"
            set_data(f"completed_orders/{order_key}", completed_order)
            orders_created.append(f"✅ {order_key} ({order_date.date()} - {day_name})")
            
            # Create confirmed order for some days
            if i < 3:
                confirmed_order = {
                    'order_id': f'test_week_confirmed_{i}',
                    'order_date': order_date.isoformat(),
                    'customer_id': f'test_customer_conf_{i}',
                    'status': 'confirmed',
                    'restaurant_name': 'Test Restaurant',
                    'delivery_address': f'Test Address Conf {i}',
                    'created_by': 'test_script'
                }
                
                conf_key = f"test_week_confirmed_{i}"
                set_data(f"confirmed_orders/{conf_key}", confirmed_order)
                orders_created.append(f"✅ {conf_key} ({order_date.date()} - {day_name})")
        
        # Create today's specific orders
        today_completed = {
            'order_id': 'test_today_completed',
            'completion_date': now.isoformat(),
            'order_date': now.isoformat(),
            'delivery_fee': 75.0,
            'customer_id': 'test_today_customer',
            'status': 'completed',
            'restaurant_name': 'Today Restaurant',
            'delivery_address': 'Today Address',
            'created_by': 'test_script'
        }
        
        set_data("completed_orders/test_today_completed", today_completed)
        orders_created.append(f"✅ test_today_completed ({now.date()} - TODAY)")
        
        print(f"\nCreated {len(orders_created)} test orders:")
        for order in orders_created:
            print(f"  {order}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test orders: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weekly_analytics():
    """Test weekly analytics after creating orders"""
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        print("\n🧮 Testing Weekly Analytics...")
        
        stats, daily_breakdown = calculate_accurate_analytics_for_period('weekly')
        
        print(f"Weekly Analytics Results:")
        print(f"  Period: {stats.get('period')}")
        print(f"  Date range: {stats.get('start_date')} to {stats.get('end_date')}")
        print(f"  Total orders: {stats.get('total_orders', 0)}")
        print(f"  Completed orders: {stats.get('completed_orders', 0)}")
        print(f"  In-progress orders: {stats.get('in_progress_orders', 0)}")
        print(f"  Pending orders: {stats.get('pending_orders', 0)}")
        print(f"  Total earnings: {stats.get('total_earnings', 0):.2f}")
        print(f"  Completion rate: {stats.get('completion_rate', 0)}%")
        
        if stats.get('total_orders', 0) > 0:
            print("\n✅ SUCCESS: Weekly analytics now shows data!")
            return True
        else:
            print("\n❌ FAILED: Weekly analytics still shows zero")
            return False
        
    except Exception as e:
        print(f"❌ Error testing weekly analytics: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🔧 CREATE TEST WEEKLY ORDERS")
    print("=" * 40)
    
    if create_current_week_orders():
        print("\n" + "=" * 40)
        if test_weekly_analytics():
            print("\n🎉 SUCCESS: Weekly analytics fix completed!")
            print("\nNext steps:")
            print("1. Restart web interface: python web_management.py")
            print("2. Check weekly analytics: http://localhost:5000/analytics?period=weekly")
            print("3. Should now show non-zero values")
        else:
            print("\n⚠️ Weekly analytics still not working - check logs")
    else:
        print("\n❌ Failed to create test orders")

if __name__ == "__main__":
    main()
