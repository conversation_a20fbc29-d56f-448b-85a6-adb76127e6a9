#!/usr/bin/env python
"""
Test script to verify the dashboard template fix
Tests that the dashboard loads without Jinja2 template errors
"""

import os
import sys
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dashboard_template_rendering():
    """Test dashboard template renders without errors"""
    print("🎨 Testing Dashboard Template Rendering...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            with app.test_request_context():
                # Test dashboard route
                response = client.get('/')
                
                if response.status_code == 302:
                    print("✅ Dashboard route exists (redirects to login as expected)")
                    return True
                elif response.status_code == 200:
                    print("✅ Dashboard route loads successfully")
                    return True
                elif response.status_code == 500:
                    print("❌ Dashboard returns 500 error - template issue")
                    return False
                else:
                    print(f"⚠️  Dashboard returns unexpected status: {response.status_code}")
                    return False
        
    except Exception as e:
        print(f"❌ Dashboard template test failed: {e}")
        return False

def test_date_formatting():
    """Test date formatting function"""
    print("\n📅 Testing Date Formatting...")
    
    try:
        # Test the date formatting used in the dashboard
        current_date = datetime.now().strftime("%B %d, %Y")
        print(f"✅ Date format works: {current_date}")
        
        # Verify format matches expected pattern
        if len(current_date) > 10 and ',' in current_date:
            print("✅ Date format is correct (Month DD, YYYY)")
            return True
        else:
            print("❌ Date format is incorrect")
            return False
        
    except Exception as e:
        print(f"❌ Date formatting test failed: {e}")
        return False

def test_template_compilation():
    """Test template compilation without rendering"""
    print("\n🔧 Testing Template Compilation...")
    
    try:
        from web_management import app
        from flask import render_template_string
        
        # Test basic template compilation
        with app.app_context():
            # Read the dashboard template
            with open('templates/dashboard.html', 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # Check for problematic Jinja2 syntax
            if 'moment()' in template_content:
                print("❌ Template still contains moment() function")
                return False
            
            if '{{ current_date }}' in template_content:
                print("✅ Template uses correct current_date variable")
            else:
                print("❌ Template missing current_date variable")
                return False
            
            print("✅ Template compilation check passed")
            return True
        
    except Exception as e:
        print(f"❌ Template compilation test failed: {e}")
        return False

def test_dashboard_function():
    """Test dashboard function directly"""
    print("\n⚙️ Testing Dashboard Function...")
    
    try:
        from web_management import app, User
        from flask_login import login_user
        
        with app.test_client() as client:
            with app.test_request_context():
                # Mock login
                user = User("test", "test", "admin")
                login_user(user)
                
                # Import dashboard function
                from web_management import dashboard
                
                # Test dashboard function
                try:
                    response = dashboard()
                    print("✅ Dashboard function executes without errors")
                    return True
                except Exception as e:
                    print(f"❌ Dashboard function error: {e}")
                    return False
        
    except Exception as e:
        print(f"❌ Dashboard function test failed: {e}")
        return False

def test_template_variables():
    """Test template variable passing"""
    print("\n📊 Testing Template Variables...")
    
    try:
        from datetime import datetime
        
        # Test variables that should be passed to template
        current_date = datetime.now().strftime("%B %d, %Y")
        stats = {
            'total_personnel': 0,
            'active_personnel': 0,
            'completed_today': 0,
            'in_progress_today': 0,
            'pending_today': 0,
            'total_daily_orders': 0
        }
        
        print(f"✅ current_date: {current_date}")
        print(f"✅ stats keys: {list(stats.keys())}")
        
        # Verify all required variables are present
        required_vars = ['current_date', 'stats']
        for var in required_vars:
            if var == 'current_date' and current_date:
                print(f"✅ {var} is properly formatted")
            elif var == 'stats' and isinstance(stats, dict):
                print(f"✅ {var} is properly structured")
            else:
                print(f"❌ {var} is missing or invalid")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Template variables test failed: {e}")
        return False

def main():
    """Run all dashboard fix tests"""
    print("🧪 DASHBOARD TEMPLATE FIX VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("Template Compilation", test_template_compilation),
        ("Date Formatting", test_date_formatting),
        ("Template Variables", test_template_variables),
        ("Dashboard Function", test_dashboard_function),
        ("Dashboard Template Rendering", test_dashboard_template_rendering)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 DASHBOARD TEMPLATE FIX SUCCESSFUL!")
        print("\n✅ Fixed Issues:")
        print("• Removed invalid moment() function from template")
        print("• Added proper current_date variable from Flask route")
        print("• Dashboard should now load without Jinja2 errors")
        print("• Date displays in correct format (Month DD, YYYY)")
        print("\n🚀 Dashboard is ready to use!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("❌ Dashboard template fix needs attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}: Dashboard template fix {'completed' if success else 'needs work'}")
    sys.exit(0 if success else 1)
