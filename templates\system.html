{% extends "base.html" %}

{% block title %}System Management - Wiz-Aroma Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        <i class="bi bi-gear"></i> System Management
    </h1>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" onclick="refreshSystemStatus()">
            <i class="bi bi-arrow-clockwise"></i> Refresh Status
        </button>
        <button type="button" class="btn btn-outline-warning" onclick="performSystemMaintenance()">
            <i class="bi bi-tools"></i> Maintenance
        </button>
    </div>
</div>

<!-- System Status Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><i class="bi bi-check-circle"></i></h4>
                <p class="mb-0">Web Interface</p>
                <small>Online</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><i class="bi bi-database"></i></h4>
                <p class="mb-0">Firebase Database</p>
                <small>{{ stats.firebase_status }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><i class="bi bi-robot"></i></h4>
                <p class="mb-0">Telegram Bots</p>
                <small>External</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4><i class="bi bi-clock"></i></h4>
                <p class="mb-0">System Uptime</p>
                <small>{{ stats.system_uptime }}</small>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> System Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Web Interface Version:</strong></td>
                        <td>1.0.0</td>
                    </tr>
                    <tr>
                        <td><strong>Flask Version:</strong></td>
                        <td>2.3.0+</td>
                    </tr>
                    <tr>
                        <td><strong>Firebase Status:</strong></td>
                        <td><span class="badge bg-success">Connected</span></td>
                    </tr>
                    <tr>
                        <td><strong>Last Backup:</strong></td>
                        <td>{{ stats.last_backup }}</td>
                    </tr>
                    <tr>
                        <td><strong>Total Collections:</strong></td>
                        <td>{{ stats.total_collections }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-activity"></i> System Health</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database Connection</span>
                    <span class="badge bg-success">Healthy</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Authentication System</span>
                    <span class="badge bg-success">Active</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Session Management</span>
                    <span class="badge bg-success">Working</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Data Synchronization</span>
                    <span class="badge bg-success">Real-time</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Security Status</span>
                    <span class="badge bg-success">Secure</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Management -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-database-gear"></i> Data Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="bi bi-people display-4 text-primary"></i>
                                <h6 class="mt-2">Personnel Data</h6>
                                <p class="text-muted">Manage delivery personnel records</p>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="managePersonnelData()">
                                    <i class="bi bi-gear"></i> Manage
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="bi bi-box-seam display-4 text-success"></i>
                                <h6 class="mt-2">Order Data</h6>
                                <p class="text-muted">Manage order records and history</p>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="manageOrderData()">
                                    <i class="bi bi-gear"></i> Manage
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="bi bi-currency-dollar display-4 text-warning"></i>
                                <h6 class="mt-2">Earnings Data</h6>
                                <p class="text-muted">Manage earnings and payroll data</p>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="manageEarningsData()">
                                    <i class="bi bi-gear"></i> Manage
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-tools"></i> System Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h6><i class="bi bi-shield-check"></i> Security Actions</h6>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="changeAdminPassword()">
                                <i class="bi bi-key"></i> Change Admin Password
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="viewLoginHistory()">
                                <i class="bi bi-clock-history"></i> View Login History
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="clearSessions()">
                                <i class="bi bi-x-circle"></i> Clear All Sessions
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6><i class="bi bi-database"></i> Data Actions</h6>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-success" onclick="backupData()">
                                <i class="bi bi-download"></i> Backup Data
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="syncData()">
                                <i class="bi bi-arrow-repeat"></i> Sync with Firebase
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="cleanupOldData()">
                                <i class="bi bi-trash"></i> Cleanup Old Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bot Integration Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-robot"></i> Bot Integration Status</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> Integration Information</h6>
                    <p>The web management interface integrates with the existing Telegram bot system:</p>
                    <ul class="mb-0">
                        <li><strong>User Bot:</strong> Handles customer orders and interactions</li>
                        <li><strong>Delivery Bot:</strong> Manages delivery personnel assignments</li>
                        <li><strong>Order Tracking Bot:</strong> Provides order status updates</li>
                        <li><strong>Management Bot:</strong> Replaced by this web interface</li>
                    </ul>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="bi bi-person display-4 text-primary"></i>
                                <h6 class="mt-2">User Bot</h6>
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="bi bi-truck display-4 text-success"></i>
                                <h6 class="mt-2">Delivery Bot</h6>
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="bi bi-geo-alt display-4 text-info"></i>
                                <h6 class="mt-2">Tracking Bot</h6>
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshSystemStatus() {
    const refreshBtn = document.querySelector('button[onclick="refreshSystemStatus()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function performSystemMaintenance() {
    if (confirm('Are you sure you want to perform system maintenance? This may temporarily affect system performance.')) {
        alert('System maintenance functionality will be implemented');
    }
}

function managePersonnelData() {
    window.location.href = '/personnel';
}

function manageOrderData() {
    window.location.href = '/orders';
}

function manageEarningsData() {
    window.location.href = '/earnings';
}

function changeAdminPassword() {
    const newPassword = prompt('Enter new admin password:');
    if (newPassword && newPassword.length >= 6) {
        alert('Password change functionality will be implemented');
    } else if (newPassword) {
        alert('Password must be at least 6 characters long');
    }
}

function viewLoginHistory() {
    alert('Login history functionality will show admin login records');
}

function clearSessions() {
    if (confirm('Are you sure you want to clear all active sessions? All users will be logged out.')) {
        alert('Session clearing functionality will be implemented');
    }
}

function backupData() {
    if (confirm('Create a backup of all system data?')) {
        alert('Data backup functionality will export all Firebase data');
    }
}

function syncData() {
    const syncBtn = event.target;
    const originalText = syncBtn.innerHTML;
    syncBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Syncing...';
    syncBtn.disabled = true;
    
    setTimeout(() => {
        syncBtn.innerHTML = originalText;
        syncBtn.disabled = false;
        alert('Data synchronization completed');
    }, 2000);
}

function cleanupOldData() {
    if (confirm('Are you sure you want to cleanup old data? This will remove records older than 6 months.')) {
        alert('Data cleanup functionality will remove old records');
    }
}

// Auto-refresh system status every 5 minutes
setInterval(refreshSystemStatus, 300000);
</script>
{% endblock %}
