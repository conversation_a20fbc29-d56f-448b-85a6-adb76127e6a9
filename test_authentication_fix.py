#!/usr/bin/env python
"""
Test script to verify the authentication fix for the Wiz-Aroma Web Management Interface
"""

import os
import sys

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_user_class():
    """Test the User class implementation"""
    print("🔐 Testing User Class Implementation...")
    
    try:
        from web_management import User
        
        # Create a test user
        user = User("test_id", "test_user", "admin")
        
        # Test basic properties
        assert user.get_id() == "test_id", "get_id() method failed"
        assert user.username == "test_user", "username property failed"
        assert user.role == "admin", "role property failed"
        
        # Test Flask-Login required properties
        assert hasattr(user, 'is_active'), "is_active property missing"
        assert hasattr(user, 'is_authenticated'), "is_authenticated property missing"
        assert hasattr(user, 'is_anonymous'), "is_anonymous property missing"
        
        # Test property getters
        assert user.is_active == True, "is_active getter failed"
        assert user.is_authenticated == True, "is_authenticated getter failed"
        assert user.is_anonymous == False, "is_anonymous getter failed"
        
        # Test property setters (this was the original issue)
        user.is_active = False
        assert user.is_active == False, "is_active setter failed"
        
        user.is_authenticated = False
        assert user.is_authenticated == False, "is_authenticated setter failed"
        
        user.is_anonymous = True
        assert user.is_anonymous == True, "is_anonymous setter failed"
        
        print("✅ User class implementation is correct")
        return True
        
    except Exception as e:
        print(f"❌ User class test failed: {e}")
        return False

def test_authentication_function():
    """Test the authentication function"""
    print("\n🔑 Testing Authentication Function...")
    
    try:
        from web_management import authenticate_user
        
        # Test valid credentials
        user = authenticate_user('admin', 'admin123')
        if user:
            print("✅ Default admin authentication successful")
            assert user.username == 'admin', "Username mismatch"
            assert user.role == 'admin', "Role mismatch"
            assert user.is_active == True, "User should be active"
        else:
            print("❌ Default admin authentication failed")
            return False
        
        # Test invalid credentials
        invalid_user = authenticate_user('admin', 'wrongpassword')
        if invalid_user is None:
            print("✅ Invalid password correctly rejected")
        else:
            print("❌ Invalid password was accepted")
            return False
        
        # Test non-existent user
        nonexistent_user = authenticate_user('nonexistent', 'password')
        if nonexistent_user is None:
            print("✅ Non-existent user correctly rejected")
        else:
            print("❌ Non-existent user was accepted")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication function test failed: {e}")
        return False

def test_flask_login_integration():
    """Test Flask-Login integration"""
    print("\n🌐 Testing Flask-Login Integration...")
    
    try:
        from web_management import app, User
        from flask_login import login_user, current_user
        
        with app.test_client() as client:
            with app.test_request_context():
                # Create a test user
                user = User("test_id", "test_user", "admin")
                
                # Test login_user function (this was failing before)
                try:
                    login_user(user)
                    print("✅ login_user() function works correctly")
                except Exception as e:
                    print(f"❌ login_user() function failed: {e}")
                    return False
                
                # Test current_user
                if current_user.is_authenticated:
                    print("✅ current_user.is_authenticated works")
                else:
                    print("❌ current_user.is_authenticated failed")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Flask-Login integration test failed: {e}")
        return False

def test_login_route():
    """Test the login route"""
    print("\n🚪 Testing Login Route...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            # Test GET request to login page
            response = client.get('/login')
            if response.status_code == 200:
                print("✅ Login page loads successfully")
            else:
                print(f"❌ Login page failed to load: {response.status_code}")
                return False
            
            # Test POST request with valid credentials
            response = client.post('/login', data={
                'username': 'admin',
                'password': 'admin123'
            }, follow_redirects=True)
            
            if response.status_code == 200:
                print("✅ Login POST request processed successfully")
                # Check if we were redirected to dashboard
                if b'Management Dashboard' in response.data or b'dashboard' in response.data:
                    print("✅ Successfully redirected to dashboard after login")
                else:
                    print("⚠️  Login processed but may not have redirected correctly")
            else:
                print(f"❌ Login POST request failed: {response.status_code}")
                return False
            
            # Test POST request with invalid credentials
            response = client.post('/login', data={
                'username': 'admin',
                'password': 'wrongpassword'
            })
            
            if response.status_code == 200:
                print("✅ Invalid credentials correctly handled")
            else:
                print(f"❌ Invalid credentials handling failed: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Login route test failed: {e}")
        return False

def main():
    """Run all authentication tests"""
    print("🧪 AUTHENTICATION FIX VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("User Class Implementation", test_user_class),
        ("Authentication Function", test_authentication_function),
        ("Flask-Login Integration", test_flask_login_integration),
        ("Login Route", test_login_route)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Authentication fix is successful")
        print("✅ You should now be able to log in with admin/admin123")
        print("\n🚀 Next steps:")
        print("1. Stop the web server if it's running")
        print("2. Restart: python web_management.py")
        print("3. Try logging in again with admin/admin123")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("❌ Authentication fix needs more work")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
