#!/usr/bin/env python3
"""
Basic test script to verify session recovery system without complex initialization.
"""

import sys
import os
import time
from unittest.mock import Mock

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_session_functions():
    """Test basic session functions without full system initialization"""
    print("🔧 Testing Basic Session Functions")
    print("-" * 40)
    
    try:
        # Import specific functions without triggering full initialization
        from bots.management_bot import (
            get_user_session,
            update_user_session,
            is_session_corrupted,
            reset_user_session,
            user_sessions,
            session_lock
        )
        
        user_id = 7729984017
        
        # Clear any existing session
        with session_lock:
            if user_id in user_sessions:
                del user_sessions[user_id]
        
        # Test session creation
        session = get_user_session(user_id)
        if session and session['state'] == 'ACTIVE':
            print("✅ User session created successfully")
        else:
            print("❌ User session creation failed")
            return False
        
        # Test session update
        update_user_session(user_id, callback_count=5, expired_count=2)
        updated_session = get_user_session(user_id)
        if updated_session['callback_count'] == 5 and updated_session['expired_count'] == 2:
            print("✅ Session update successful")
        else:
            print("❌ Session update failed")
            return False
        
        # Test corruption detection
        update_user_session(user_id, expired_count=3)  # Trigger corruption
        if is_session_corrupted(user_id):
            print("✅ Session corruption detected correctly")
        else:
            print("❌ Session corruption not detected")
            return False
        
        # Test session reset
        reset_user_session(user_id, "test_reset")
        reset_session = get_user_session(user_id)
        if (reset_session['callback_count'] == 0 and 
            reset_session['expired_count'] == 0 and 
            reset_session['state'] == 'ACTIVE'):
            print("✅ Session reset successful")
        else:
            print("❌ Session reset failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Basic session functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_deduplication():
    """Test callback deduplication without full system"""
    print("\n🔄 Testing Callback Deduplication")
    print("-" * 40)
    
    try:
        from bots.management_bot import (
            is_callback_duplicate,
            should_batch_callback,
            processed_callbacks,
            callback_batch_buffer,
            callback_lock
        )
        
        # Clear state
        with callback_lock:
            processed_callbacks.clear()
            callback_batch_buffer.clear()
        
        # Create mock callback
        mock_call = Mock()
        mock_call.id = "test_callback_123"
        mock_call.data = "mgmt_analytics"
        mock_call.from_user = Mock()
        mock_call.from_user.id = 7729984017
        
        # Test first callback (should not be duplicate)
        if not is_callback_duplicate(mock_call):
            print("✅ First callback not marked as duplicate")
        else:
            print("❌ First callback incorrectly marked as duplicate")
            return False
        
        # Test same callback again (should be duplicate)
        if is_callback_duplicate(mock_call):
            print("✅ Duplicate callback correctly detected")
        else:
            print("❌ Duplicate callback not detected")
            return False
        
        # Test callback batching
        mock_call2 = Mock()
        mock_call2.id = "test_callback_124"
        mock_call2.data = "mgmt_analytics_daily"
        mock_call2.from_user = Mock()
        mock_call2.from_user.id = 7729984017
        
        # First call should not be batched
        if not should_batch_callback(mock_call2):
            print("✅ First callback not batched")
        else:
            print("❌ First callback incorrectly batched")
            return False
        
        # Immediate second call should be batched
        if should_batch_callback(mock_call2):
            print("✅ Rapid successive callback correctly batched")
        else:
            print("❌ Rapid successive callback not batched")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Callback deduplication test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_circuit_breaker():
    """Test circuit breaker functionality"""
    print("\n⚡ Testing Circuit Breaker")
    print("-" * 40)
    
    try:
        from bots.management_bot import (
            update_circuit_breaker,
            is_circuit_breaker_open,
            circuit_breaker
        )
        
        # Reset circuit breaker
        circuit_breaker.update({
            'failure_count': 0,
            'last_failure_time': 0,
            'state': 'CLOSED'
        })
        
        # Test normal operation
        if not is_circuit_breaker_open():
            print("✅ Circuit breaker initially closed")
        else:
            print("❌ Circuit breaker should be closed initially")
            return False
        
        # Test failure accumulation
        for i in range(5):  # Trigger threshold
            update_circuit_breaker(failed=True)
        
        if circuit_breaker['state'] == 'OPEN':
            print("✅ Circuit breaker opened after failures")
        else:
            print("❌ Circuit breaker should be open after failures")
            return False
        
        # Test success after recovery
        circuit_breaker['state'] = 'HALF_OPEN'  # Simulate recovery timeout
        update_circuit_breaker(failed=False)
        if circuit_breaker['state'] == 'CLOSED':
            print("✅ Circuit breaker closed after success")
        else:
            print("❌ Circuit breaker should close after success")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Circuit breaker test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_queue_metrics():
    """Test queue metrics without full queue processing"""
    print("\n📊 Testing Queue Metrics")
    print("-" * 40)
    
    try:
        from bots.management_bot import (
            get_queue_metrics,
            callback_queue,
            callback_lock
        )
        
        # Clear queue
        with callback_lock:
            callback_queue.clear()
        
        # Test empty queue metrics
        metrics = get_queue_metrics()
        if metrics['queue_size'] == 0:
            print("✅ Empty queue metrics correct")
        else:
            print("❌ Empty queue metrics incorrect")
            return False
        
        # Add mock callbacks to queue
        current_time = time.time()
        mock_call = Mock()
        mock_call.from_user = Mock()
        mock_call.from_user.id = 7729984017
        
        with callback_lock:
            callback_queue.append((current_time, 1, mock_call))
            callback_queue.append((current_time - 5, 0, mock_call))
        
        # Test populated queue metrics
        metrics = get_queue_metrics()
        if metrics['queue_size'] == 2:
            print("✅ Populated queue metrics correct")
        else:
            print(f"❌ Populated queue metrics incorrect: {metrics}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Queue metrics test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_basic_tests():
    """Run basic tests without full system initialization"""
    print("🚀 Basic Session Recovery System Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Session Functions", test_basic_session_functions),
        ("Callback Deduplication", test_callback_deduplication),
        ("Circuit Breaker", test_circuit_breaker),
        ("Queue Metrics", test_queue_metrics)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed!")
        print("\n✨ Core Components Verified:")
        print("  • Session state management and corruption detection")
        print("  • Callback deduplication and batching")
        print("  • Circuit breaker pattern functionality")
        print("  • Queue metrics and monitoring")
        print("\n🔧 Ready for Integration:")
        print("  • Queue-based callback processing system")
        print("  • Automatic session recovery mechanisms")
        print("  • Real-time monitoring and health checks")
    else:
        print("⚠️ Some basic tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
