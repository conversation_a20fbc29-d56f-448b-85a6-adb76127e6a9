#!/usr/bin/env python
"""
Test script for the enhanced Wiz-Aroma Web Management Interface
Tests order management, order history, personnel management, and phone number features
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, delete_data, initialize_firebase
from src.config import logger

def test_firebase_connection():
    """Test Firebase connection"""
    print("🔥 Testing Firebase Connection...")
    
    try:
        if initialize_firebase():
            print("✅ Firebase initialized successfully")
            return True
        else:
            print("❌ Firebase initialization failed")
            return False
    except Exception as e:
        print(f"❌ Firebase connection error: {e}")
        return False

def test_order_status_management():
    """Test order status management functionality"""
    print("\n📦 Testing Order Status Management...")
    
    try:
        from web_management import get_order_status_details
        
        # Test order status categorization
        orders_by_status = get_order_status_details()
        
        expected_categories = ['in_progress', 'completed', 'pending', 'confirmed']
        for category in expected_categories:
            if category in orders_by_status:
                print(f"✅ Order category '{category}' found with {len(orders_by_status[category])} orders")
            else:
                print(f"❌ Order category '{category}' missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Order status management test failed: {e}")
        return False

def test_personnel_management_firebase():
    """Test personnel management Firebase integration"""
    print("\n👥 Testing Personnel Management Firebase Integration...")
    
    try:
        from web_management import get_authorized_delivery_personnel, add_authorized_delivery_personnel
        
        # Test getting personnel data
        personnel = get_authorized_delivery_personnel()
        print(f"📊 Retrieved {len(personnel)} personnel records from Firebase")
        
        # Test adding personnel with phone number
        test_telegram_id = "999888777"
        test_name = "Test Personnel Enhanced"
        test_phone = "0912345678"
        
        success, message = add_authorized_delivery_personnel(
            test_telegram_id, test_name, "test_admin", test_phone
        )
        
        if success:
            print(f"✅ Personnel addition with phone number successful: {message}")
            
            # Verify the data was saved with phone number
            saved_personnel = get_authorized_delivery_personnel()
            if test_telegram_id in saved_personnel:
                saved_data = saved_personnel[test_telegram_id]
                if saved_data.get('phone_number') == test_phone:
                    print("✅ Phone number saved correctly")
                else:
                    print(f"❌ Phone number not saved correctly: {saved_data.get('phone_number')}")
                    return False
            
            # Clean up test data
            delete_data(f"delivery_personnel/delivery_personnel_{test_telegram_id}")
            delete_data(f"delivery_personnel_earnings/delivery_personnel_{test_telegram_id}")
            print("🧹 Test data cleaned up")
        else:
            print(f"❌ Personnel addition failed: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Personnel management Firebase test failed: {e}")
        return False

def test_phone_number_validation():
    """Test phone number validation"""
    print("\n📞 Testing Phone Number Validation...")
    
    try:
        from src.utils.helpers import is_valid_phone
        
        # Test valid Ethiopian phone numbers
        valid_phones = [
            "0912345678",
            "0923456789",
            "0934567890",
            "+251912345678",
            "251912345678"
        ]
        
        # Test invalid phone numbers
        invalid_phones = [
            "123456789",      # Too short
            "09123456789",    # Too long
            "0812345678",     # Wrong prefix
            "abc1234567",     # Contains letters
            "+1234567890"     # Wrong country code
        ]
        
        for phone in valid_phones:
            if is_valid_phone(phone):
                print(f"✅ Valid phone accepted: {phone}")
            else:
                print(f"❌ Valid phone rejected: {phone}")
                return False
        
        for phone in invalid_phones:
            if not is_valid_phone(phone):
                print(f"✅ Invalid phone rejected: {phone}")
            else:
                print(f"❌ Invalid phone accepted: {phone}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Phone number validation test failed: {e}")
        return False

def test_web_routes():
    """Test new web routes"""
    print("\n🌐 Testing Web Routes...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            # Test order history route
            response = client.get('/orders/history')
            if response.status_code == 302:  # Redirect to login
                print("✅ Order history route exists (redirects to login)")
            else:
                print(f"❌ Order history route failed: {response.status_code}")
                return False
            
            # Test API order status route
            response = client.get('/api/orders/status')
            if response.status_code == 302:  # Redirect to login
                print("✅ API order status route exists (redirects to login)")
            else:
                print(f"❌ API order status route failed: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Web routes test failed: {e}")
        return False

def test_real_time_updates():
    """Test real-time update functionality"""
    print("\n⚡ Testing Real-time Update Functionality...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            # Test if the API endpoint returns proper JSON
            with app.test_request_context():
                from flask_login import login_user
                from web_management import User
                
                # Mock login
                user = User("test", "test", "admin")
                login_user(user)
                
                response = client.get('/api/orders/status')
                if response.status_code == 200:
                    data = response.get_json()
                    expected_fields = ['in_progress_count', 'pending_count', 'completed_count', 'last_updated']
                    
                    for field in expected_fields:
                        if field in data:
                            print(f"✅ API field '{field}' present: {data[field]}")
                        else:
                            print(f"❌ API field '{field}' missing")
                            return False
                else:
                    print(f"❌ API endpoint failed: {response.status_code}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Real-time updates test failed: {e}")
        return False

def test_data_integrity():
    """Test data integrity after enhancements"""
    print("\n🔍 Testing Data Integrity...")
    
    try:
        # Check if existing data structures are preserved
        personnel_data = get_data("delivery_personnel") or {}
        earnings_data = get_data("delivery_personnel_earnings") or {}
        
        print(f"📊 Personnel records: {len(personnel_data)}")
        print(f"💰 Earnings records: {len(earnings_data)}")
        
        # Check data structure consistency
        for personnel_id, data in personnel_data.items():
            required_fields = ['telegram_id', 'name', 'status']
            for field in required_fields:
                if field not in data:
                    print(f"❌ Personnel {personnel_id} missing required field: {field}")
                    return False
        
        print("✅ Data integrity maintained")
        return True
        
    except Exception as e:
        print(f"❌ Data integrity test failed: {e}")
        return False

def main():
    """Run all enhancement tests"""
    print("🧪 WIZ-AROMA WEB MANAGEMENT ENHANCEMENTS TEST")
    print("=" * 60)
    
    tests = [
        ("Firebase Connection", test_firebase_connection),
        ("Order Status Management", test_order_status_management),
        ("Personnel Management Firebase", test_personnel_management_firebase),
        ("Phone Number Validation", test_phone_number_validation),
        ("Web Routes", test_web_routes),
        ("Real-time Updates", test_real_time_updates),
        ("Data Integrity", test_data_integrity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL ENHANCEMENT TESTS PASSED!")
        print("\n✨ New Features Ready:")
        print("• Order status management with real-time updates")
        print("• Separate order history page")
        print("• Enhanced personnel management with Firebase integration")
        print("• Phone number support with Ethiopian validation")
        print("• Real-time order status API")
        print("\n🚀 Restart the web interface to use new features!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("❌ Some enhancements need attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
