{% extends "base.html" %}

{% block title %}Add Personnel - Wiz-Aroma Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        <i class="bi bi-person-plus"></i> Add New Personnel
    </h1>
    <a href="{{ url_for('personnel') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> Back to Personnel
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-badge"></i> Personnel Registration
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="addPersonnelForm">
                    <div class="mb-3">
                        <label for="telegram_id" class="form-label">
                            <i class="bi bi-telegram"></i> Telegram ID <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="telegram_id" name="telegram_id"
                            placeholder="Enter Telegram ID (numbers only)" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            The unique Telegram ID of the delivery personnel. This will be used for bot authorization.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="bi bi-person"></i> Full Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" placeholder="Enter full name"
                            required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            The display name for the delivery personnel.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="phone_number" class="form-label">
                            <i class="bi bi-telephone"></i> Phone Number
                        </label>
                        <input type="tel" class="form-control" id="phone_number" name="phone_number"
                            placeholder="Enter phone number (e.g., 09xxxxxxxx)">
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            Ethiopian phone number format (optional but recommended).
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="bi bi-lightbulb"></i> How to find Telegram ID:</h6>
                        <ol class="mb-0">
                            <li>Ask the delivery person to message <code>@userinfobot</code> on Telegram</li>
                            <li>The bot will reply with their Telegram ID</li>
                            <li>Enter that ID number in the field above</li>
                        </ol>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Important Notes:</h6>
                        <ul class="mb-0">
                            <li>The Telegram ID must be unique (not already registered)</li>
                            <li>Once added, the personnel will be authorized to use the delivery bot</li>
                            <li>An earnings record will be automatically created</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('personnel') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Add Personnel
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Registration Process Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> What Happens After Registration
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-shield-check text-success"></i> Automatic Setup</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check text-success"></i> Delivery bot authorization</li>
                            <li><i class="bi bi-check text-success"></i> Earnings record creation</li>
                            <li><i class="bi bi-check text-success"></i> Assignment tracking setup</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-person-workspace text-primary"></i> Personnel Access</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check text-success"></i> Can receive order broadcasts</li>
                            <li><i class="bi bi-check text-success"></i> Can accept/decline orders</li>
                            <li><i class="bi bi-check text-success"></i> Earnings tracking enabled</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function isValidEthiopianPhone(phone) {
        // Ethiopian phone number validation
        // Formats: 09xxxxxxxx, +251xxxxxxxxx, 251xxxxxxxxx
        const patterns = [
            /^09\d{8}$/,           // 09xxxxxxxx
            /^\+2519\d{8}$/,       // +2519xxxxxxxx
            /^2519\d{8}$/          // 2519xxxxxxxx
        ];

        return patterns.some(pattern => pattern.test(phone));
    }

    document.getElementById('addPersonnelForm').addEventListener('submit', function (e) {
        const telegramId = document.getElementById('telegram_id').value.trim();
        const name = document.getElementById('name').value.trim();
        const phoneNumber = document.getElementById('phone_number').value.trim();

        // Validate Telegram ID
        if (!telegramId || !telegramId.match(/^\d+$/)) {
            e.preventDefault();
            alert('Please enter a valid Telegram ID (numbers only)');
            document.getElementById('telegram_id').focus();
            return;
        }

        // Validate name
        if (!name || name.length < 2) {
            e.preventDefault();
            alert('Please enter a valid name (at least 2 characters)');
            document.getElementById('name').focus();
            return;
        }

        // Validate phone number if provided
        if (phoneNumber && !isValidEthiopianPhone(phoneNumber)) {
            e.preventDefault();
            alert('Please enter a valid Ethiopian phone number (e.g., 09xxxxxxxx)');
            document.getElementById('phone_number').focus();
            return;
        }

        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Adding Personnel...';
        submitBtn.disabled = true;
    });

    // Real-time validation
    document.getElementById('telegram_id').addEventListener('input', function (e) {
        const value = e.target.value;
        const isValid = value.match(/^\d*$/);

        if (!isValid && value !== '') {
            e.target.classList.add('is-invalid');
            if (!document.getElementById('telegram_id_error')) {
                const errorDiv = document.createElement('div');
                errorDiv.id = 'telegram_id_error';
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = 'Telegram ID must contain only numbers';
                e.target.parentNode.appendChild(errorDiv);
            }
        } else {
            e.target.classList.remove('is-invalid');
            const errorDiv = document.getElementById('telegram_id_error');
            if (errorDiv) {
                errorDiv.remove();
            }
        }
    });

    document.getElementById('name').addEventListener('input', function (e) {
        const value = e.target.value.trim();

        if (value.length > 0 && value.length < 2) {
            e.target.classList.add('is-invalid');
            if (!document.getElementById('name_error')) {
                const errorDiv = document.createElement('div');
                errorDiv.id = 'name_error';
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = 'Name must be at least 2 characters long';
                e.target.parentNode.appendChild(errorDiv);
            }
        } else {
            e.target.classList.remove('is-invalid');
            const errorDiv = document.getElementById('name_error');
            if (errorDiv) {
                errorDiv.remove();
            }
        }
    });
</script>
{% endblock %}