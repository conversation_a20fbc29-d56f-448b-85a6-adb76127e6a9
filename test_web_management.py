#!/usr/bin/env python
"""
Test script for the Wiz-Aroma Web Management Interface
Validates integration with existing Firebase data and bot systems
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, initialize_firebase
from src.config import logger

def test_firebase_connection():
    """Test Firebase connection and data access"""
    print("🔥 Testing Firebase Connection...")
    
    try:
        # Initialize Firebase
        if initialize_firebase():
            print("✅ Firebase initialized successfully")
        else:
            print("❌ Firebase initialization failed")
            return False
        
        # Test data access
        test_data = {"test": "web_management_test", "timestamp": datetime.utcnow().isoformat()}
        set_data("web_management_test", test_data)
        
        retrieved_data = get_data("web_management_test")
        if retrieved_data and retrieved_data.get("test") == "web_management_test":
            print("✅ Firebase read/write operations working")
            return True
        else:
            print("❌ Firebase read/write operations failed")
            return False
            
    except Exception as e:
        print(f"❌ Firebase connection error: {e}")
        return False

def test_personnel_data_compatibility():
    """Test compatibility with existing personnel data structure"""
    print("\n👥 Testing Personnel Data Compatibility...")
    
    try:
        # Get existing personnel data
        personnel_data = get_data("delivery_personnel") or {}
        print(f"📊 Found {len(personnel_data)} personnel records")
        
        # Test data structure compatibility
        for personnel_id, data in personnel_data.items():
            required_fields = ['telegram_id', 'name', 'status']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                print(f"⚠️  Personnel {personnel_id} missing fields: {missing_fields}")
            else:
                print(f"✅ Personnel {personnel_id} data structure valid")
        
        # Test earnings data compatibility
        earnings_data = get_data("delivery_personnel_earnings") or {}
        print(f"💰 Found {len(earnings_data)} earnings records")
        
        return True
        
    except Exception as e:
        print(f"❌ Personnel data compatibility error: {e}")
        return False

def test_analytics_data_compatibility():
    """Test compatibility with existing analytics data"""
    print("\n📊 Testing Analytics Data Compatibility...")
    
    try:
        # Test all analytics data sources
        data_sources = [
            "completed_orders",
            "confirmed_orders", 
            "delivery_personnel_assignments",
            "delivery_personnel_earnings"
        ]
        
        for source in data_sources:
            data = get_data(source) or {}
            print(f"📈 {source}: {len(data)} records")
        
        print("✅ Analytics data sources accessible")
        return True
        
    except Exception as e:
        print(f"❌ Analytics data compatibility error: {e}")
        return False

def test_web_interface_startup():
    """Test if the web interface can start properly"""
    print("\n🌐 Testing Web Interface Startup...")
    
    try:
        # Import the web management module
        import web_management
        
        # Check if Flask app is created
        if hasattr(web_management, 'app'):
            print("✅ Flask app created successfully")
            
            # Test route registration
            routes = [rule.rule for rule in web_management.app.url_map.iter_rules()]
            expected_routes = ['/', '/login', '/personnel', '/analytics', '/orders', '/earnings', '/system']
            
            for route in expected_routes:
                if route in routes:
                    print(f"✅ Route {route} registered")
                else:
                    print(f"❌ Route {route} missing")
            
            return True
        else:
            print("❌ Flask app not created")
            return False
            
    except Exception as e:
        print(f"❌ Web interface startup error: {e}")
        return False

def test_authentication_system():
    """Test the authentication system"""
    print("\n🔐 Testing Authentication System...")
    
    try:
        from web_management import authenticate_user, User
        
        # Test default admin authentication
        user = authenticate_user('admin', 'admin123')
        if user and isinstance(user, User):
            print("✅ Default admin authentication working")
        else:
            print("❌ Default admin authentication failed")
            return False
        
        # Test invalid credentials
        invalid_user = authenticate_user('invalid', 'invalid')
        if invalid_user is None:
            print("✅ Invalid credential rejection working")
        else:
            print("❌ Invalid credential rejection failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication system error: {e}")
        return False

def test_personnel_management_functions():
    """Test personnel management functions"""
    print("\n👨‍💼 Testing Personnel Management Functions...")
    
    try:
        from web_management import get_authorized_delivery_personnel, add_authorized_delivery_personnel
        
        # Test getting personnel
        personnel = get_authorized_delivery_personnel()
        print(f"📋 Retrieved {len(personnel)} personnel records")
        
        # Test adding personnel (with test data)
        test_telegram_id = "999999999"  # Test ID
        test_name = "Test Personnel"
        
        success, message = add_authorized_delivery_personnel(test_telegram_id, test_name, "test_admin")
        if success:
            print(f"✅ Personnel addition successful: {message}")
            
            # Clean up test data
            from src.firebase_db import delete_data
            delete_data(f"delivery_personnel/delivery_personnel_{test_telegram_id}")
            delete_data(f"delivery_personnel_earnings/delivery_personnel_{test_telegram_id}")
            print("🧹 Test data cleaned up")
        else:
            print(f"❌ Personnel addition failed: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Personnel management functions error: {e}")
        return False

def test_data_integrity():
    """Test data integrity and consistency"""
    print("\n🔍 Testing Data Integrity...")
    
    try:
        # Check for orphaned records
        personnel_data = get_data("delivery_personnel") or {}
        earnings_data = get_data("delivery_personnel_earnings") or {}
        
        personnel_ids = set(personnel_data.keys())
        earnings_ids = set(earnings_data.keys())
        
        # Check for personnel without earnings records
        missing_earnings = personnel_ids - earnings_ids
        if missing_earnings:
            print(f"⚠️  Personnel without earnings records: {len(missing_earnings)}")
        else:
            print("✅ All personnel have earnings records")
        
        # Check for earnings without personnel records
        orphaned_earnings = earnings_ids - personnel_ids
        if orphaned_earnings:
            print(f"⚠️  Orphaned earnings records: {len(orphaned_earnings)}")
        else:
            print("✅ No orphaned earnings records")
        
        return True
        
    except Exception as e:
        print(f"❌ Data integrity check error: {e}")
        return False

def generate_test_report():
    """Generate a comprehensive test report"""
    print("\n" + "="*60)
    print("🧪 WIZ-AROMA WEB MANAGEMENT INTERFACE TEST REPORT")
    print("="*60)
    
    tests = [
        ("Firebase Connection", test_firebase_connection),
        ("Personnel Data Compatibility", test_personnel_data_compatibility),
        ("Analytics Data Compatibility", test_analytics_data_compatibility),
        ("Web Interface Startup", test_web_interface_startup),
        ("Authentication System", test_authentication_system),
        ("Personnel Management Functions", test_personnel_management_functions),
        ("Data Integrity", test_data_integrity)
    ]
    
    results = {}
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_function in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            result = test_function()
            results[test_name] = result
            if result:
                passed_tests += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            results[test_name] = False
            print(f"❌ {test_name}: ERROR - {e}")
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! Web interface is ready for deployment.")
        print("\n📝 Next Steps:")
        print("1. Run the web interface: python web_management.py")
        print("2. Access the interface at: http://localhost:5000")
        print("3. Login with: admin / admin123")
        print("4. Test all features in the web interface")
    else:
        print("\n⚠️  Some tests failed. Please review the errors above.")
        print("Fix the issues before deploying the web interface.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    print("🚀 Starting Wiz-Aroma Web Management Interface Tests...")
    success = generate_test_report()
    
    if success:
        print("\n🌟 Web Management Interface is ready!")
        print("Run: python web_management.py")
    else:
        print("\n🔧 Please fix the issues and run tests again.")
    
    sys.exit(0 if success else 1)
