{% extends "base.html" %}

{% block title %}Edit Personnel - Wiz-Aroma Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        <i class="bi bi-person-gear"></i> Edit Personnel
    </h1>
    <a href="{{ url_for('personnel') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> Back to Personnel
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-badge"></i> Edit Personnel Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editPersonnelForm">
                    <div class="mb-3">
                        <label for="telegram_id" class="form-label">
                            <i class="bi bi-telegram"></i> Telegram ID
                        </label>
                        <input type="text" class="form-control" id="telegram_id" value="{{ personnel.telegram_id }}"
                            readonly>
                        <div class="form-text">
                            <i class="bi bi-lock"></i>
                            Telegram ID cannot be changed after registration.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="bi bi-person"></i> Full Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ personnel.name }}"
                            placeholder="Enter full name" required>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            The display name for the delivery personnel.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="phone_number" class="form-label">
                            <i class="bi bi-telephone"></i> Phone Number
                        </label>
                        <input type="tel" class="form-control" id="phone_number" name="phone_number"
                            value="{{ personnel.phone_number or '' }}"
                            placeholder="Enter phone number (e.g., 09xxxxxxxx)">
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            Ethiopian phone number format (optional but recommended).
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="bi bi-toggle-on"></i> Status
                        </label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" {% if personnel.status=='active' %}selected{% endif %}>
                                <i class="bi bi-check-circle"></i> Active
                            </option>
                            <option value="inactive" {% if personnel.status=='inactive' %}selected{% endif %}>
                                <i class="bi bi-pause-circle"></i> Inactive
                            </option>
                            <option value="suspended" {% if personnel.status=='suspended' %}selected{% endif %}>
                                <i class="bi bi-x-circle"></i> Suspended
                            </option>
                        </select>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i>
                            Only active personnel can receive order assignments.
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> Personnel Information:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Added Date:</strong><br>
                                <span class="text-muted">
                                    {% if personnel.added_date %}
                                    {{ personnel.added_date[:10] }}
                                    {% else %}
                                    Unknown
                                    {% endif %}
                                </span>
                            </div>
                            <div class="col-md-6">
                                <strong>Added By:</strong><br>
                                <span class="text-muted">
                                    {% if personnel.added_by %}
                                    Admin ID: {{ personnel.added_by }}
                                    {% else %}
                                    Unknown
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Status Change Effects:</h6>
                        <ul class="mb-0">
                            <li><strong>Active:</strong> Can receive and accept orders</li>
                            <li><strong>Inactive:</strong> Temporarily disabled, no new orders</li>
                            <li><strong>Suspended:</strong> Blocked from all delivery activities</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('personnel') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Personnel
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Personnel Statistics Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Personnel Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">0</h4>
                            <small class="text-muted">Total Deliveries</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h4 class="text-success">0 Birr</h4>
                            <small class="text-muted">Total Earnings</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h4 class="text-info">0%</h4>
                            <small class="text-muted">Success Rate</small>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        Statistics will be available after the personnel completes their first delivery.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function isValidEthiopianPhone(phone) {
        // Ethiopian phone number validation
        // Formats: 09xxxxxxxx, +251xxxxxxxxx, 251xxxxxxxxx
        const patterns = [
            /^09\d{8}$/,           // 09xxxxxxxx
            /^\+2519\d{8}$/,       // +2519xxxxxxxx
            /^2519\d{8}$/          // 2519xxxxxxxx
        ];

        return patterns.some(pattern => pattern.test(phone));
    }

    document.getElementById('editPersonnelForm').addEventListener('submit', function (e) {
        const name = document.getElementById('name').value.trim();
        const phoneNumber = document.getElementById('phone_number').value.trim();

        // Validate name
        if (!name || name.length < 2) {
            e.preventDefault();
            alert('Please enter a valid name (at least 2 characters)');
            document.getElementById('name').focus();
            return;
        }

        // Validate phone number if provided
        if (phoneNumber && !isValidEthiopianPhone(phoneNumber)) {
            e.preventDefault();
            alert('Please enter a valid Ethiopian phone number (e.g., 09xxxxxxxx)');
            document.getElementById('phone_number').focus();
            return;
        }

        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Updating Personnel...';
        submitBtn.disabled = true;
    });

    // Real-time validation
    document.getElementById('name').addEventListener('input', function (e) {
        const value = e.target.value.trim();

        if (value.length > 0 && value.length < 2) {
            e.target.classList.add('is-invalid');
            if (!document.getElementById('name_error')) {
                const errorDiv = document.createElement('div');
                errorDiv.id = 'name_error';
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = 'Name must be at least 2 characters long';
                e.target.parentNode.appendChild(errorDiv);
            }
        } else {
            e.target.classList.remove('is-invalid');
            const errorDiv = document.getElementById('name_error');
            if (errorDiv) {
                errorDiv.remove();
            }
        }
    });

    // Status change confirmation
    document.getElementById('status').addEventListener('change', function (e) {
        const newStatus = e.target.value;
        const currentStatus = '{{ personnel.status }}';

        if (newStatus !== currentStatus && (newStatus === 'suspended' || newStatus === 'inactive')) {
            const confirmMessage = newStatus === 'suspended'
                ? 'Are you sure you want to suspend this personnel? They will be blocked from all delivery activities.'
                : 'Are you sure you want to deactivate this personnel? They will not receive new orders.';

            if (!confirm(confirmMessage)) {
                e.target.value = currentStatus;
            }
        }
    });
</script>
{% endblock %}