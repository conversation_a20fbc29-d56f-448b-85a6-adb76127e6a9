#!/usr/bin/env python
"""
Fix weekly data synchronization by creating current week test data and analyzing existing data
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, delete_data, initialize_firebase
from src.config import logger

def analyze_existing_firebase_data():
    """Analyze what's currently in Firebase"""
    print("🔍 Analyzing Existing Firebase Data...")
    
    try:
        # Get all collections
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        pending_admin_reviews = get_data("pending_admin_reviews") or {}
        current_orders = get_data("current_orders") or {}
        
        print(f"Firebase Collections:")
        print(f"  completed_orders: {len(completed_orders)} records")
        print(f"  confirmed_orders: {len(confirmed_orders)} records")
        print(f"  pending_admin_reviews: {len(pending_admin_reviews)} records")
        print(f"  current_orders: {len(current_orders)} records")
        
        # Analyze dates in completed orders
        print(f"\nCompleted Orders Date Analysis:")
        all_dates = []
        
        for order_id, order_data in completed_orders.items():
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
            print(f"  {order_id}: date='{order_date_str}', fee={order_data.get('delivery_fee', 'N/A')}")
            
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()
                    all_dates.append(order_date)
                except Exception as e:
                    print(f"    ❌ Date parsing failed: {e}")
        
        if all_dates:
            print(f"\nDate Range in Data:")
            print(f"  Earliest: {min(all_dates)}")
            print(f"  Latest: {max(all_dates)}")
            print(f"  Unique dates: {sorted(set(all_dates))}")
        
        # Check current week range
        now = datetime.now()
        current_weekday = now.weekday()  # Monday = 0
        monday_this_week = (now - timedelta(days=current_weekday)).date()
        
        print(f"\nCurrent Week Analysis:")
        print(f"  Today: {now.date()} ({now.strftime('%A')})")
        print(f"  Monday of this week: {monday_this_week}")
        print(f"  Week range: {monday_this_week} to {now.date()}")
        
        # Check which orders fall in current week
        current_week_orders = []
        for order_date in all_dates:
            if monday_this_week <= order_date <= now.date():
                current_week_orders.append(order_date)
        
        print(f"  Orders in current week: {len(current_week_orders)}")
        if current_week_orders:
            print(f"  Current week order dates: {sorted(current_week_orders)}")
        
        return len(current_week_orders) > 0
        
    except Exception as e:
        print(f"❌ Failed to analyze Firebase data: {e}")
        return False

def create_current_week_test_data():
    """Create test data for the current week"""
    print("\n📅 Creating Current Week Test Data...")
    
    try:
        now = datetime.now()
        current_weekday = now.weekday()  # Monday = 0
        monday_this_week = now - timedelta(days=current_weekday)
        
        print(f"Creating orders for current week:")
        print(f"  Today: {now.date()} ({now.strftime('%A')})")
        print(f"  Monday: {monday_this_week.date()}")
        
        # Create orders for each day from Monday to today
        created_orders = []
        
        for i in range(current_weekday + 1):  # From Monday (0) to today
            order_date = monday_this_week + timedelta(days=i)
            day_name = order_date.strftime('%A')
            
            # Create completed order
            completed_order = {
                'order_id': f'week_completed_{i}_{day_name.lower()}',
                'completion_date': order_date.isoformat(),
                'order_date': order_date.isoformat(),
                'delivery_fee': 50.0 + (i * 5),  # Varying fees
                'customer_id': f'week_customer_{i}',
                'status': 'completed',
                'restaurant_name': 'Test Restaurant',
                'delivery_address': f'Test Address {i}'
            }
            
            order_key = f"week_completed_{i}_{day_name.lower()}"
            set_data(f"completed_orders/{order_key}", completed_order)
            created_orders.append(f"Completed: {order_key} ({order_date.date()})")
            
            # Create confirmed order for some days
            if i < 3:  # Only for first 3 days
                confirmed_order = {
                    'order_id': f'week_confirmed_{i}_{day_name.lower()}',
                    'order_date': order_date.isoformat(),
                    'customer_id': f'week_customer_conf_{i}',
                    'status': 'confirmed',
                    'restaurant_name': 'Test Restaurant',
                    'delivery_address': f'Test Address Conf {i}'
                }
                
                conf_key = f"week_confirmed_{i}_{day_name.lower()}"
                set_data(f"confirmed_orders/{conf_key}", confirmed_order)
                created_orders.append(f"Confirmed: {conf_key} ({order_date.date()})")
        
        # Create today's orders specifically
        today_completed = {
            'order_id': 'today_completed_order',
            'completion_date': now.isoformat(),
            'order_date': now.isoformat(),
            'delivery_fee': 75.0,
            'customer_id': 'today_customer',
            'status': 'completed',
            'restaurant_name': 'Today Restaurant',
            'delivery_address': 'Today Address'
        }
        
        set_data("completed_orders/today_completed_order", today_completed)
        created_orders.append(f"Today Completed: today_completed_order ({now.date()})")
        
        today_confirmed = {
            'order_id': 'today_confirmed_order',
            'order_date': now.isoformat(),
            'customer_id': 'today_customer_conf',
            'status': 'confirmed',
            'restaurant_name': 'Today Restaurant',
            'delivery_address': 'Today Address Conf'
        }
        
        set_data("confirmed_orders/today_confirmed_order", today_confirmed)
        created_orders.append(f"Today Confirmed: today_confirmed_order ({now.date()})")
        
        print(f"\n✅ Created {len(created_orders)} test orders:")
        for order in created_orders:
            print(f"  {order}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create current week test data: {e}")
        return False

def test_weekly_calculation_with_new_data():
    """Test weekly calculation with the new data"""
    print("\n🧮 Testing Weekly Calculation with New Data...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Test weekly calculation
        stats, daily_breakdown = calculate_accurate_analytics_for_period('weekly')
        
        print("Weekly Analytics Results:")
        print(f"  Period: {stats.get('period', 'unknown')}")
        print(f"  Date range: {stats.get('start_date')} to {stats.get('end_date')}")
        print(f"  Total orders: {stats.get('total_orders', 0)}")
        print(f"  Completed orders: {stats.get('completed_orders', 0)}")
        print(f"  In-progress orders: {stats.get('in_progress_orders', 0)}")
        print(f"  Pending orders: {stats.get('pending_orders', 0)}")
        print(f"  Completion rate: {stats.get('completion_rate', 0)}%")
        print(f"  Total earnings: {stats.get('total_earnings', 0):.2f}")
        
        print(f"\nDaily Breakdown:")
        if daily_breakdown:
            for date, data in sorted(daily_breakdown.items()):
                total_day = data.get('completed', 0) + data.get('in_progress', 0) + data.get('pending', 0)
                print(f"  {date}: completed={data.get('completed', 0)}, in_progress={data.get('in_progress', 0)}, pending={data.get('pending', 0)}, total={total_day}")
        else:
            print("  No daily breakdown data")
        
        # Check if we now have data
        if stats.get('total_orders', 0) > 0:
            print("\n✅ Weekly calculation now shows data!")
            return True
        else:
            print("\n❌ Weekly calculation still shows zero orders")
            return False
        
    except Exception as e:
        print(f"❌ Weekly calculation test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_all_time_vs_weekly():
    """Compare all-time vs weekly to ensure consistency"""
    print("\n🔄 Testing All-time vs Weekly Consistency...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Get both calculations
        weekly_stats, _ = calculate_accurate_analytics_for_period('weekly')
        all_time_stats, _ = calculate_accurate_analytics_for_period('all_time')
        
        print("Comparison Results:")
        print(f"  Weekly total orders: {weekly_stats.get('total_orders', 0)}")
        print(f"  All-time total orders: {all_time_stats.get('total_orders', 0)}")
        print(f"  Weekly completed: {weekly_stats.get('completed_orders', 0)}")
        print(f"  All-time completed: {all_time_stats.get('completed_orders', 0)}")
        
        # Logical checks
        if weekly_stats.get('total_orders', 0) <= all_time_stats.get('total_orders', 0):
            print("✅ Weekly ≤ All-time (logical)")
        else:
            print("❌ Weekly > All-time (illogical)")
            return False
        
        if all_time_stats.get('total_orders', 0) > 0:
            print("✅ All-time shows data")
        else:
            print("❌ All-time shows no data")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Consistency test failed: {e}")
        return False

def main():
    """Run the weekly data sync fix"""
    print("🔧 WEEKLY DATA SYNCHRONIZATION FIX")
    print("=" * 50)
    
    steps = [
        ("Analyze Existing Firebase Data", analyze_existing_firebase_data),
        ("Create Current Week Test Data", create_current_week_test_data),
        ("Test Weekly Calculation with New Data", test_weekly_calculation_with_new_data),
        ("Test All-time vs Weekly Consistency", test_all_time_vs_weekly)
    ]
    
    passed = 0
    total = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}...")
        try:
            if step_func():
                passed += 1
                print(f"✅ {step_name}: SUCCESS")
            else:
                print(f"❌ {step_name}: FAILED")
        except Exception as e:
            print(f"❌ {step_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTS: {passed}/{total} steps completed successfully")
    
    if passed == total:
        print("\n🎉 WEEKLY DATA SYNC FIX COMPLETED!")
        print("\n✅ Changes Made:")
        print("• Fixed weekly date range to start from Monday")
        print("• Created current week test data")
        print("• Added detailed logging for debugging")
        print("• Verified data consistency")
        print("\n🚀 Next Steps:")
        print("1. Restart web interface: python web_management.py")
        print("2. Test weekly analytics: http://localhost:5000/analytics?period=weekly")
        print("3. Weekly should now show non-zero values")
    else:
        print(f"\n⚠️  {total - passed} step(s) failed")
        print("❌ Weekly data sync needs further investigation")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}: Weekly data sync {'fixed' if success else 'needs work'}")
    sys.exit(0 if success else 1)
