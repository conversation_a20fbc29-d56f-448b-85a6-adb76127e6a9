{% extends "base.html" %}

{% block title %}
{% if page_type == 'history' %}
Order History - Wiz-Aroma Management
{% else %}
Order Management - Wiz-Aroma Management
{% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        {% if page_type == 'history' %}
        <i class="bi bi-clock-history"></i> Order History
        {% else %}
        <i class="bi bi-box-seam"></i> Order Management
        {% endif %}
    </h1>
    <div class="btn-group" role="group">
        {% if page_type == 'history' %}
        <a href="{{ url_for('orders') }}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> Active Orders
        </a>
        {% else %}
        <a href="{{ url_for('order_history') }}" class="btn btn-outline-info">
            <i class="bi bi-clock-history"></i> Order History
        </a>
        {% endif %}
        <button type="button" class="btn btn-outline-primary" onclick="refreshOrders()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportOrders()">
            <i class="bi bi-download"></i> Export
        </button>
    </div>
</div>

<!-- Time Period Selector -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="mb-0">
                    <i class="bi bi-calendar-range"></i> Time Period Filter
                </h6>
                <small class="text-muted">
                    {% if page_type == 'history' %}
                    Showing completed orders only
                    {% else %}
                    Showing in-progress orders only
                    {% endif %}
                </small>
            </div>
            <div class="col-md-6">
                <div class="btn-group w-100" role="group" id="timePeriodButtons">
                    <a href="?period=daily"
                        class="btn btn-outline-primary {% if current_period == 'daily' %}active{% endif %}">
                        <i class="bi bi-calendar-day"></i> Daily
                    </a>
                    <a href="?period=weekly"
                        class="btn btn-outline-primary {% if current_period == 'weekly' %}active{% endif %}">
                        <i class="bi bi-calendar-week"></i> Weekly
                    </a>
                    <a href="?period=monthly"
                        class="btn btn-outline-primary {% if current_period == 'monthly' %}active{% endif %}">
                        <i class="bi bi-calendar-month"></i> Monthly
                    </a>
                    <a href="?period=all_time"
                        class="btn btn-outline-primary {% if current_period == 'all_time' %}active{% endif %}">
                        <i class="bi bi-calendar"></i> All Time
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Status Indicator -->
<div class="alert alert-info d-flex align-items-center mb-4" id="statusIndicator">
    <div class="spinner-border spinner-border-sm me-2" role="status" id="statusSpinner">
        <span class="visually-hidden">Loading...</span>
    </div>
    <span id="statusText">Checking for updates...</span>
    <span class="ms-auto" id="lastUpdated">Last updated: Never</span>
</div>

<!-- Order Statistics -->
<div class="row mb-4">
    {% if page_type == 'history' %}
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3 id="totalCompleted">{{ stats.total_completed or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-check-circle"></i> Total Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3 id="completedToday">{{ stats.completed_today or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-calendar-day"></i> Today</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3 id="completedWeek">{{ stats.completed_week or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-calendar-week"></i> This Week</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3 id="completedMonth">{{ stats.completed_month or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-calendar-month"></i> This Month</p>
            </div>
        </div>
    </div>
    {% else %}
    <div class="col-md-4 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3 id="inProgress">{{ stats.in_progress or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-arrow-clockwise"></i> In Progress (Incomplete)</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3 id="pending">{{ stats.pending or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-hourglass-split"></i> Pending Orders</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3 id="completed">{{ stats.completed or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-check-circle"></i> Completed
                    {% if current_period == 'daily' %}Today
                    {% elif current_period == 'weekly' %}This Week
                    {% elif current_period == 'monthly' %}This Month
                    {% else %}All Time
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">Filter by Status:</label>
                <select class="form-select" id="statusFilter" onchange="filterOrders()">
                    <option value="">All Orders</option>
                    <option value="completed">Completed</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="pending">Pending</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFilter" class="form-label">Filter by Date:</label>
                <input type="date" class="form-control" id="dateFilter" onchange="filterOrders()">
            </div>
            <div class="col-md-4">
                <label for="searchFilter" class="form-label">Search Orders:</label>
                <input type="text" class="form-control" id="searchFilter"
                    placeholder="Search by order number, customer, restaurant..." onkeyup="filterOrders()">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="bi bi-x-circle"></i> Clear
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-table"></i> Order List
        </h5>
    </div>
    <div class="card-body">
        {% if orders %}
        <div class="table-responsive">
            <table class="table table-hover" id="ordersTable">
                <thead>
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Restaurant</th>
                        <th>Items</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order_id, order in orders.items() %}
                    <tr class="order-row" data-status="{{ order.status }}"
                        data-date="{{ order.get('order_date', '') }}">
                        <td>
                            <strong>#{{ order.get('order_number', order_id[:8]) }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle p-1 me-2">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                                <div>
                                    <div>{{ order.get('customer_name', 'Unknown') }}</div>
                                    <small class="text-muted">{{ order.get('phone_number', 'No phone') }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ order.get('restaurant', 'Unknown Restaurant') }}</strong>
                                <br>
                                <small class="text-muted">{{ order.get('restaurant_area', 'Unknown Area') }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                {{ order.get('items', [])|length }} items
                            </span>
                        </td>
                        <td>
                            <strong>{{ order.get('total_price', 0) }} Birr</strong>
                            <br>
                            <small class="text-muted">
                                Delivery: {{ order.get('delivery_fee', 0) }} Birr
                            </small>
                        </td>
                        <td>
                            {% if order.status == 'completed' %}
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle"></i> Completed
                            </span>
                            {% elif order.status == 'confirmed' %}
                            <span class="badge bg-info">
                                <i class="bi bi-clock"></i> Confirmed
                            </span>
                            {% else %}
                            <span class="badge bg-warning">
                                <i class="bi bi-hourglass-split"></i> {{ order.status|title }}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if order.get('order_date') %}
                            {{ order.order_date[:10] }}
                            <br>
                            <small class="text-muted">{{ order.order_date[11:16] }}</small>
                            {% else %}
                            <span class="text-muted">Unknown</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary"
                                    onclick="viewOrderDetails('{{ order_id }}')">
                                    <i class="bi bi-eye"></i> View
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info"
                                    onclick="trackOrder('{{ order_id }}')">
                                    <i class="bi bi-geo-alt"></i> Track
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-box-seam display-1 text-muted"></i>
            <h4 class="mt-3">No Orders Found</h4>
            <p class="text-muted">Orders will appear here once customers start placing orders.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-box-seam"></i> Order Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- Order details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Real-time update variables
    let updateInterval;
    let lastUpdateTime = null;

    // Initialize real-time updates when page loads
    document.addEventListener('DOMContentLoaded', function () {
        startRealTimeUpdates();
    });

    function startRealTimeUpdates() {
        // Update immediately
        updateOrderStatus();

        // Set up periodic updates every 30 seconds
        updateInterval = setInterval(updateOrderStatus, 30000);

        // Update status indicator
        updateStatusIndicator('Connected', 'success');
    }

    function stopRealTimeUpdates() {
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = null;
        }
        updateStatusIndicator('Disconnected', 'danger');
    }

    function updateOrderStatus() {
        fetch('/api/orders/status')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                // Update statistics cards
                const pageType = '{{ page_type }}';
                if (pageType === 'active') {
                    updateElement('totalActive', data.in_progress_count + data.pending_count);
                    updateElement('inProgress', data.in_progress_count);
                    updateElement('pending', data.pending_count);
                }

                // Update last updated time
                lastUpdateTime = new Date(data.last_updated);
                updateStatusIndicator('Connected - Live updates active', 'success');
                document.getElementById('lastUpdated').textContent =
                    'Last updated: ' + lastUpdateTime.toLocaleTimeString();
            })
            .catch(error => {
                console.error('Error updating order status:', error);
                updateStatusIndicator('Connection error', 'warning');
            });
    }

    function updateElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    function updateStatusIndicator(message, type) {
        const indicator = document.getElementById('statusIndicator');
        const spinner = document.getElementById('statusSpinner');
        const statusText = document.getElementById('statusText');

        // Remove existing classes
        indicator.className = 'alert d-flex align-items-center mb-4';

        // Add appropriate class
        switch (type) {
            case 'success':
                indicator.classList.add('alert-success');
                spinner.style.display = 'none';
                break;
            case 'warning':
                indicator.classList.add('alert-warning');
                spinner.style.display = 'inline-block';
                break;
            case 'danger':
                indicator.classList.add('alert-danger');
                spinner.style.display = 'none';
                break;
            default:
                indicator.classList.add('alert-info');
                spinner.style.display = 'inline-block';
        }

        statusText.textContent = message;
    }

    function filterOrders() {
        const statusFilter = document.getElementById('statusFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

        const rows = document.querySelectorAll('.order-row');

        rows.forEach(row => {
            let show = true;

            // Status filter
            if (statusFilter && row.dataset.status !== statusFilter) {
                show = false;
            }

            // Date filter
            if (dateFilter && !row.dataset.date.startsWith(dateFilter)) {
                show = false;
            }

            // Search filter
            if (searchFilter && !row.textContent.toLowerCase().includes(searchFilter)) {
                show = false;
            }

            row.style.display = show ? '' : 'none';
        });
    }

    function clearFilters() {
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('searchFilter').value = '';
        filterOrders();
    }

    function viewOrderDetails(orderId) {
        // In production, this would fetch order details via AJAX
        const orderData = {{ orders| tojson
    }};
    const order = orderData[orderId];

    if (!order) {
        alert('Order not found');
        return;
    }

    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Order Information</h6>
                <p><strong>Order Number:</strong> #${order.order_number || orderId.substring(0, 8)}</p>
                <p><strong>Status:</strong> ${order.status}</p>
                <p><strong>Date:</strong> ${order.order_date || 'Unknown'}</p>
                <p><strong>Total:</strong> ${order.total_price || 0} Birr</p>
            </div>
            <div class="col-md-6">
                <h6>Customer Information</h6>
                <p><strong>Name:</strong> ${order.customer_name || 'Unknown'}</p>
                <p><strong>Phone:</strong> ${order.phone_number || 'No phone'}</p>
                <p><strong>Delivery Location:</strong> ${order.delivery_location || 'Unknown'}</p>
            </div>
        </div>
        <hr>
        <h6>Restaurant & Items</h6>
        <p><strong>Restaurant:</strong> ${order.restaurant || 'Unknown'}</p>
        <p><strong>Area:</strong> ${order.restaurant_area || 'Unknown'}</p>
        <p><strong>Items:</strong> ${order.items ? order.items.length : 0} items</p>
    `;

    document.getElementById('orderDetailsContent').innerHTML = content;

    const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
    modal.show();
}

    function trackOrder(orderId) {
        // In production, this would show order tracking information
        alert('Order tracking functionality will be integrated with the tracking bot');
    }

    function refreshOrders() {
        const refreshBtn = document.querySelector('button[onclick="refreshOrders()"]');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';
        refreshBtn.disabled = true;

        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    function exportOrders() {
        alert('Export functionality will generate CSV/PDF reports of orders');
    }

    // Auto-refresh every 30 seconds
    setInterval(refreshOrders, 30000);
</script>
{% endblock %}