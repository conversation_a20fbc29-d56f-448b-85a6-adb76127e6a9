#!/usr/bin/env python3
"""
Test script to verify callback query timeout fixes in the management bot.
Tests the new safe callback handling and timeout protection mechanisms.
"""

import sys
import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import the management bot functions
try:
    from bots.management_bot import (
        safe_answer_callback_query,
        safe_callback_with_retry,
        validate_callback_query,
        handle_callback_error,
        CALLBACK_QUERY_TIMEOUT
    )
    print("✅ Successfully imported management bot callback utilities")
except ImportError as e:
    print(f"❌ Failed to import management bot utilities: {e}")
    sys.exit(1)

class TestCallbackTimeoutFixes(unittest.TestCase):
    """Test cases for callback query timeout handling"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_call = Mock()
        self.mock_call.id = "test_callback_123"
        self.mock_call.data = "test_action"
        self.mock_call.message = Mock()
        self.mock_call.message.chat = Mock()
        self.mock_call.message.chat.id = 12345
        self.mock_call.message.date = time.time()  # Current time
        
    def test_validate_callback_query_valid(self):
        """Test validation of valid callback query"""
        is_valid, error_msg = validate_callback_query(self.mock_call)
        self.assertTrue(is_valid)
        self.assertIsNone(error_msg)
        print("✅ Valid callback query validation passed")
    
    def test_validate_callback_query_expired(self):
        """Test validation of expired callback query"""
        # Set message date to be older than timeout
        self.mock_call.message.date = time.time() - (CALLBACK_QUERY_TIMEOUT + 10)
        
        is_valid, error_msg = validate_callback_query(self.mock_call)
        self.assertFalse(is_valid)
        self.assertIn("expired", error_msg.lower())
        print("✅ Expired callback query validation passed")
    
    def test_validate_callback_query_invalid_data(self):
        """Test validation of callback query with invalid data"""
        self.mock_call.data = None
        
        is_valid, error_msg = validate_callback_query(self.mock_call)
        self.assertFalse(is_valid)
        self.assertIn("invalid callback data", error_msg.lower())
        print("✅ Invalid callback data validation passed")
    
    @patch('bots.management_bot.management_bot')
    def test_safe_answer_callback_query_success(self, mock_bot):
        """Test successful callback query answering"""
        mock_bot.answer_callback_query.return_value = True
        
        result = safe_answer_callback_query(self.mock_call, "Test message")
        
        self.assertTrue(result)
        mock_bot.answer_callback_query.assert_called_once_with(
            self.mock_call.id, "Test message", show_alert=False
        )
        print("✅ Safe callback query answering passed")
    
    @patch('bots.management_bot.management_bot')
    def test_safe_answer_callback_query_expired(self, mock_bot):
        """Test callback query answering with expired query"""
        # Set message date to be older than timeout
        self.mock_call.message.date = time.time() - (CALLBACK_QUERY_TIMEOUT + 10)
        
        result = safe_answer_callback_query(
            self.mock_call, 
            "Test message", 
            fallback_message="Fallback message"
        )
        
        # Should not call answer_callback_query for expired queries
        mock_bot.answer_callback_query.assert_not_called()
        # Should send fallback message instead
        mock_bot.send_message.assert_called_once_with(
            self.mock_call.message.chat.id, "Fallback message"
        )
        print("✅ Expired callback query handling passed")
    
    @patch('bots.management_bot.management_bot')
    def test_safe_answer_callback_query_api_error(self, mock_bot):
        """Test callback query answering with API error"""
        # Simulate "query is too old" error
        mock_bot.answer_callback_query.side_effect = Exception("Bad Request: query is too old and response timeout expired")
        
        result = safe_answer_callback_query(
            self.mock_call, 
            "Test message", 
            fallback_message="Fallback message"
        )
        
        # Should attempt to answer callback query
        mock_bot.answer_callback_query.assert_called_once()
        # Should send fallback message when API error occurs
        mock_bot.send_message.assert_called_once_with(
            self.mock_call.message.chat.id, "Fallback message"
        )
        print("✅ API error handling passed")
    
    @patch('bots.management_bot.management_bot')
    def test_safe_callback_with_retry(self, mock_bot):
        """Test callback query with retry logic"""
        # First call fails, second succeeds
        mock_bot.answer_callback_query.side_effect = [
            Exception("Temporary error"),
            True
        ]
        
        with patch('bots.management_bot.safe_answer_callback_query') as mock_safe:
            mock_safe.side_effect = [False, True]  # First fails, second succeeds
            
            result = safe_callback_with_retry(self.mock_call, "Test message", max_retries=2)
            
            self.assertTrue(result)
            self.assertEqual(mock_safe.call_count, 2)
        print("✅ Callback retry logic passed")
    
    def test_handle_callback_error_timeout(self):
        """Test error handling for timeout errors"""
        with patch('bots.management_bot.safe_answer_callback_query') as mock_safe:
            mock_safe.return_value = True
            
            result = handle_callback_error(
                self.mock_call, 
                Exception("Connection timeout"), 
                "test_function"
            )
            
            self.assertTrue(result)
            # Should call safe_answer_callback_query with timeout message
            mock_safe.assert_called_once()
            args, kwargs = mock_safe.call_args
            self.assertIn("timeout", args[1].lower())
        print("✅ Timeout error handling passed")
    
    def test_handle_callback_error_database(self):
        """Test error handling for database errors"""
        with patch('bots.management_bot.safe_answer_callback_query') as mock_safe:
            mock_safe.return_value = True
            
            result = handle_callback_error(
                self.mock_call, 
                Exception("Firebase connection failed"), 
                "test_function"
            )
            
            self.assertTrue(result)
            # Should call safe_answer_callback_query with database message
            mock_safe.assert_called_once()
            args, kwargs = mock_safe.call_args
            self.assertIn("database", args[1].lower())
        print("✅ Database error handling passed")

def run_callback_timeout_tests():
    """Run all callback timeout tests"""
    print("🚀 Starting Callback Query Timeout Fix Tests")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCallbackTimeoutFixes)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # Print results
    print(f"\n📊 Test Results:")
    print(f"✅ Tests passed: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Tests failed: {len(result.failures)}")
    print(f"💥 Tests with errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All callback timeout fix tests passed!")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return success

if __name__ == "__main__":
    success = run_callback_timeout_tests()
    sys.exit(0 if success else 1)
