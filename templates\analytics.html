{% extends "base.html" %}

{% block title %}Analytics Dashboard - Wiz-Aroma Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        <i class="bi bi-graph-up"></i> Analytics Dashboard
    </h1>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportData()">
            <i class="bi bi-download"></i> Export
        </button>
    </div>
</div>

<!-- Analytics Header with Data Source -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="bi bi-graph-up me-2"></i>
        <strong>Analytics Dashboard - {{ stats.current_date or 'Unknown Date' }}</strong>
        <span class="ms-auto">
            <small>
                <i class="bi bi-database"></i> {{ stats.data_source or 'Firebase Firestore' }}
                <br>
                <i class="bi bi-clock"></i> Last updated: {{ stats.last_updated or 'Unknown' }}
            </small>
            <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="refreshAnalytics()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="debugAnalytics()">
                <i class="bi bi-bug"></i> Debug
            </button>
            <a href="/debug-dashboard" class="btn btn-sm btn-outline-info ms-1" target="_blank">
                <i class="bi bi-tools"></i> Debug Dashboard
            </a>
            {% if current_period == 'weekly' %}
            <button type="button" class="btn btn-sm btn-outline-warning ms-1" onclick="testWeeklySync()">
                <i class="bi bi-calendar-week"></i> Test Weekly Sync
            </button>
            <button type="button" class="btn btn-sm btn-outline-success ms-1" onclick="createWeeklyTestData()">
                <i class="bi bi-plus-circle"></i> Create Test Data
            </button>
            {% endif %}
        </span>
    </div>
</div>

<!-- Time Period Selector -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="mb-0">
                    <i class="bi bi-calendar-range"></i> Analytics Time Period
                </h6>
                <small class="text-muted">
                    Currently showing:
                    {% if current_period == 'daily' %}Today's data ({{ stats.current_date }})
                    {% elif current_period == 'weekly' %}Last 7 days
                    {% elif current_period == 'monthly' %}Last 30 days
                    {% else %}All time data
                    {% endif %}
                    {% if stats.start_date and stats.end_date %}
                    <br>Period: {{ stats.start_date }} to {{ stats.end_date }}
                    {% endif %}
                </small>
            </div>
            <div class="col-md-6">
                <div class="btn-group w-100" role="group" id="timePeriodButtons">
                    <a href="?period=daily"
                        class="btn btn-outline-primary {% if current_period == 'daily' %}active{% endif %}">
                        <i class="bi bi-calendar-day"></i> Daily
                    </a>
                    <a href="?period=weekly"
                        class="btn btn-outline-primary {% if current_period == 'weekly' %}active{% endif %}">
                        <i class="bi bi-calendar-week"></i> Weekly
                    </a>
                    <a href="?period=monthly"
                        class="btn btn-outline-primary {% if current_period == 'monthly' %}active{% endif %}">
                        <i class="bi bi-calendar-month"></i> Monthly
                    </a>
                    <a href="?period=all_time"
                        class="btn btn-outline-primary {% if current_period == 'all_time' %}active{% endif %}">
                        <i class="bi bi-calendar"></i> All Time
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3 id="totalOrders">{{ stats.total_orders or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-box-seam"></i> Total Orders</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3 id="completedOrders">{{ stats.completed_orders or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-check-circle"></i> Completed Orders</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3 id="inProgressOrders">{{ stats.in_progress_orders or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-arrow-clockwise"></i> In Progress</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3 id="completionRate">{{ stats.completion_rate or 0 }}%</h3>
                <p class="mb-0"><i class="bi bi-graph-up"></i> Completion Rate</p>
            </div>
        </div>
    </div>
</div>

<!-- Additional Metrics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h3 id="pendingOrders">{{ stats.pending_orders or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-hourglass-split"></i> Pending Orders</p>
                <small>Awaiting processing</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-dark text-white">
            <div class="card-body text-center">
                <h3 id="activePersonnel">{{ stats.active_personnel or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-people"></i> Active Personnel</p>
                <small>Total: {{ stats.total_personnel or 0 }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card" style="background: linear-gradient(135deg, #FF9800, #F57C00); color: white;">
            <div class="card-body text-center">
                <h3 id="totalEarnings">{{ "%.2f"|format(stats.total_earnings or 0) }}</h3>
                <p class="mb-0"><i class="bi bi-currency-dollar"></i> Earnings (Birr)</p>
                <small>50% delivery share</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3 id="efficiency">{{ stats.completion_rate or 0 }}%</h3>
                <p class="mb-0"><i class="bi bi-speedometer2"></i> Efficiency</p>
                <small>Completion rate</small>
            </div>
        </div>
    </div>
</div>

<!-- Firebase Data Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-database"></i> Data Summary - {{ current_period|title }} Period</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <div class="border rounded p-2 bg-success text-white">
                            <strong>{{ stats.completed_orders or 0 }}</strong>
                            <br><small>Completed</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="border rounded p-2 bg-warning text-white">
                            <strong>{{ stats.in_progress_orders or 0 }}</strong>
                            <br><small>In Progress</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="border rounded p-2 bg-danger text-white">
                            <strong>{{ stats.pending_orders or 0 }}</strong>
                            <br><small>Pending</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="border rounded p-2 bg-primary text-white">
                            <strong>{{ stats.total_orders or 0 }}</strong>
                            <br><small>Total Orders</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="border rounded p-2 bg-info text-white">
                            <strong>{{ stats.completion_rate or 0 }}%</strong>
                            <br><small>Success Rate</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="border rounded p-2 bg-dark text-white">
                            <strong>{{ "%.0f"|format(stats.total_earnings or 0) }}</strong>
                            <br><small>Earnings (Birr)</small>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            Raw Firebase Collections:
                            {{ analytics_data.firebase_collections.delivery_personnel or 0 }} Personnel,
                            {{ analytics_data.firebase_collections.completed_orders or 0 }} Completed Total,
                            {{ analytics_data.firebase_collections.confirmed_orders or 0 }} Confirmed Total,
                            {{ analytics_data.firebase_collections.pending_admin_reviews or 0 }} Pending Reviews,
                            {{ analytics_data.firebase_collections.current_orders or 0 }} Current Orders
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Orders Trend Chart -->
    <div class="col-md-8 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-graph-up"></i> Orders Trend</h5>
            </div>
            <div class="card-body">
                <canvas id="ordersTrendChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Order Status Distribution -->
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pie-chart"></i> Order Status</h5>
            </div>
            <div class="card-body">
                <canvas id="orderStatusChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Personnel Performance -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person-badge"></i> Personnel Performance</h5>
            </div>
            <div class="card-body">
                <canvas id="personnelPerformanceChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Earnings Distribution -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-currency-dollar"></i> Earnings Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="earningsChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Analytics Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-table"></i> Detailed Analytics</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="analyticsTable">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Today</th>
                        <th>This Week</th>
                        <th>This Month</th>
                        <th>All Time</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Total Orders</strong></td>
                        <td id="ordersToday">0</td>
                        <td id="ordersWeek">0</td>
                        <td id="ordersMonth">0</td>
                        <td id="ordersAllTime">{{ stats.total_orders or 0 }}</td>
                    </tr>
                    <tr>
                        <td><strong>Confirmed Orders</strong></td>
                        <td id="confirmedToday">0</td>
                        <td id="confirmedWeek">0</td>
                        <td id="confirmedMonth">0</td>
                        <td id="confirmedAllTime">{{ stats.total_confirmed or 0 }}</td>
                    </tr>
                    <tr>
                        <td><strong>Total Earnings (Birr)</strong></td>
                        <td id="earningsToday">0.00</td>
                        <td id="earningsWeek">0.00</td>
                        <td id="earningsMonth">0.00</td>
                        <td id="earningsAllTime">{{ "%.2f"|format(stats.total_earnings or 0) }}</td>
                    </tr>
                    <tr>
                        <td><strong>Average Order Value</strong></td>
                        <td id="avgOrderToday">0.00</td>
                        <td id="avgOrderWeek">0.00</td>
                        <td id="avgOrderMonth">0.00</td>
                        <td id="avgOrderAllTime">0.00</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Real Firebase analytics data
    const analyticsData = {{ analytics_data| tojson }};
    const dailyBreakdown = {{ daily_breakdown| tojson }};
    const currentPeriod = '{{ current_period }}';

    // Analytics refresh and monitoring
    let analyticsRefreshInterval;
    let ordersTrendChart, orderStatusChart, personnelPerformanceChart, earningsChart;

    // Initialize analytics when page loads
    document.addEventListener('DOMContentLoaded', function () {
        initializeAnalyticsCharts();
        startAnalyticsAutoRefresh();
        updateAnalyticsStatus();
    });

    function initializeAnalyticsCharts() {

        // Orders Trend Chart with real Firebase data
        const ordersTrendCtx = document.getElementById('ordersTrendChart').getContext('2d');

        // Prepare data from daily breakdown
        const trendLabels = [];
        const completedData = [];
        const inProgressData = [];
        const pendingData = [];

        // Sort dates and prepare chart data
        const sortedDates = Object.keys(dailyBreakdown || {}).sort();
        sortedDates.forEach(date => {
            const dateObj = new Date(date);
            trendLabels.push(dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            completedData.push(dailyBreakdown[date].completed || 0);
            inProgressData.push(dailyBreakdown[date].in_progress || 0);
            pendingData.push(dailyBreakdown[date].pending || 0);
        });

        // If no data, show current period summary
        if (trendLabels.length === 0) {
            trendLabels.push(currentPeriod.charAt(0).toUpperCase() + currentPeriod.slice(1));
            completedData.push({{ stats.completed_orders or 0 }});
        inProgressData.push({{ stats.in_progress_orders or 0 }});
    pendingData.push({{ stats.pending_orders or 0 }});
        }

    ordersTrendChart = new Chart(ordersTrendCtx, {
        type: 'line',
        data: {
            labels: trendLabels,
            datasets: [{
                label: 'Completed Orders',
                data: completedData,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'In Progress',
                data: inProgressData,
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Pending',
                data: pendingData,
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Order Status Chart
    const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
    orderStatusChart = new Chart(orderStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Completed', 'Confirmed', 'Pending'],
            datasets: [{
                data: [{{ stats.total_orders or 0 }}, {{ stats.total_confirmed or 0 }}, 0],
        backgroundColor: ['#4CAF50', '#2196F3', '#FF9800'],
        borderWidth: 0
    }]
    },
        options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

    // Personnel Performance Chart
    const personnelPerformanceCtx = document.getElementById('personnelPerformanceChart').getContext('2d');
    personnelPerformanceChart = new Chart(personnelPerformanceCtx, {
        type: 'bar',
        data: {
            labels: ['Personnel 1', 'Personnel 2', 'Personnel 3'],
            datasets: [{
                label: 'Deliveries',
                data: [12, 8, 15],
                backgroundColor: '#4CAF50',
                borderRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Earnings Chart
    const earningsCtx = document.getElementById('earningsChart').getContext('2d');
    earningsChart = new Chart(earningsCtx, {
        type: 'bar',
        data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [{
                label: 'Earnings (Birr)',
                data: [1200, 1500, 1100, 1800],
                backgroundColor: '#FF9800',
                borderRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Time period change handler
    document.querySelectorAll('input[name="timePeriod"]').forEach(radio => {
        radio.addEventListener('change', function () {
            updateAnalytics(this.value);
        });
    });

    function updateAnalytics(period) {
        // In production, this would make an AJAX call to get filtered data
        console.log('Updating analytics for period:', period);

        // Update charts and tables based on selected period
        // This is where you'd implement the actual data filtering
    }

    function refreshData() {
        // Show loading state
        const refreshBtn = document.querySelector('button[onclick="refreshData()"]');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';
        refreshBtn.disabled = true;

        // Simulate refresh (in production, make AJAX call)
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    function exportData() {
        // Export analytics data to CSV
        const csvData = [
            ['Metric', 'Value'],
            ['Period', currentPeriod],
            ['Total Orders', {{ stats.total_orders or 0 }}],
    ['Completed Orders', {{ stats.completed_orders or 0 }}],
        ['In Progress Orders', {{ stats.in_progress_orders or 0 }}],
        ['Pending Orders', {{ stats.pending_orders or 0 }}],
        ['Completion Rate', '{{ stats.completion_rate or 0 }}%'],
        ['Total Earnings', '{{ "%.2f"|format(stats.total_earnings or 0) }} Birr'],
        ['Active Personnel', {{ stats.active_personnel or 0 }}],
        ['Total Personnel', {{ stats.total_personnel or 0 }}]
        ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `wiz-aroma-analytics-${currentPeriod}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    }

    function startAnalyticsAutoRefresh() {
        // Auto-refresh every 3 minutes
        analyticsRefreshInterval = setInterval(refreshAnalytics, 180000);
    }

    function stopAnalyticsAutoRefresh() {
        if (analyticsRefreshInterval) {
            clearInterval(analyticsRefreshInterval);
            analyticsRefreshInterval = null;
        }
    }

    function refreshAnalytics() {
        // Reload page with current period to get fresh data
        window.location.href = `?period=${currentPeriod}`;
    }

    function debugAnalytics() {
        // Open debug endpoint in new tab
        const debugUrl = `/api/analytics/debug?period=${currentPeriod}`;
        window.open(debugUrl, '_blank');
    }

    function testWeeklySync() {
        // Open weekly sync test endpoint in new tab
        const weeklySyncUrl = '/api/analytics/weekly-sync-test';
        window.open(weeklySyncUrl, '_blank');
    }

    function createWeeklyTestData() {
        if (confirm('Create test orders for the current week to fix weekly analytics?\n\nThis will add sample orders with dates from Monday to today.')) {
            fetch('/api/analytics/create-weekly-test-data')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Success! Created ${data.created_orders.length} test orders.\n\nImmediate test results:\n- Total orders: ${data.immediate_test_results.total_orders}\n- Completed orders: ${data.immediate_test_results.completed_orders}\n- Total earnings: $${data.immediate_test_results.total_earnings}\n\nRefreshing page...`);
                        location.reload();
                    } else {
                        alert('Error creating test data: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error creating test data: ' + error.message);
                });
        }
    }

    function updateAnalyticsStatus() {
        // Check data source and update status
        const dataSource = '{{ stats.data_source or "" }}';
        if (dataSource.includes('Error') || dataSource.includes('fallback')) {
            console.warn('Analytics using fallback data');
        } else {
            console.log('Analytics using real-time Firebase data');
        }

        // Log current analytics data for debugging
        console.log('Current Analytics Data:', {
            period: currentPeriod,
            dataSource: '{{ stats.data_source or "Unknown" }}'
        });
    }

    // Monitor page visibility for auto-refresh
    document.addEventListener('visibilitychange', function () {
        if (document.hidden) {
            stopAnalyticsAutoRefresh();
        } else {
            startAnalyticsAutoRefresh();
            // Refresh data when page becomes visible again
            if (document.visibilityState === 'visible') {
                setTimeout(refreshAnalytics, 2000);
            }
        }
    });
</script>
{% endblock %}