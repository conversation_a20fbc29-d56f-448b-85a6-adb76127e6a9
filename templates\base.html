<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Wiz-Aroma Management{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --wiz-primary: #4CAF50;
            --wiz-secondary: #2E7D32;
            --wiz-accent: #FFC107;
            --wiz-dark: #1B5E20;
            --wiz-light: #E8F5E8;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--wiz-primary) !important;
        }

        .navbar {
            background: linear-gradient(135deg, var(--wiz-primary), var(--wiz-secondary)) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar .nav-link {
            color: white !important;
            font-weight: 500;
        }

        .navbar .nav-link:hover {
            color: var(--wiz-accent) !important;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease-in-out;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--wiz-primary), var(--wiz-secondary));
            color: white;
            border-radius: 12px 12px 0 0 !important;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--wiz-primary), var(--wiz-secondary));
            border: none;
            border-radius: 8px;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--wiz-secondary), var(--wiz-dark));
            transform: translateY(-1px);
        }

        .btn-success {
            background: var(--wiz-primary);
            border: none;
            border-radius: 8px;
        }

        .btn-warning {
            background: var(--wiz-accent);
            border: none;
            border-radius: 8px;
            color: #333;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background: var(--wiz-light);
            border: none;
            font-weight: 600;
            color: var(--wiz-dark);
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link {
            color: #333;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 4px 8px;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: var(--wiz-light);
            color: var(--wiz-dark);
            font-weight: 600;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--wiz-primary), var(--wiz-secondary));
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-card p {
            margin: 0;
            opacity: 0.9;
        }

        .content-wrapper {
            padding: 20px;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-bottom: 20px;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-shop"></i> Wiz-Aroma Management
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('personnel') }}">
                            <i class="bi bi-people"></i> Personnel
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('analytics') }}">
                            <i class="bi bi-graph-up"></i> Analytics
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-box-seam"></i> Orders
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('orders') }}">
                                    <i class="bi bi-arrow-clockwise"></i> Active Orders
                                </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('order_history') }}">
                                    <i class="bi bi-clock-history"></i> Order History
                                </a></li>
                        </ul>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="bi bi-box-arrow-right"></i> Logout
                                </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <div class="container-fluid mt-3">
        {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
            role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% block sidebar %}
            <div class="col-md-3 col-lg-2 mt-3">
                <div class="sidebar">
                    <nav class="nav flex-column py-3">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                        <a class="nav-link" href="{{ url_for('personnel') }}">
                            <i class="bi bi-people"></i> Personnel Management
                        </a>
                        <a class="nav-link" href="{{ url_for('analytics') }}">
                            <i class="bi bi-graph-up"></i> Analytics & Reports
                        </a>
                        <a class="nav-link" href="{{ url_for('orders') }}">
                            <i class="bi bi-arrow-clockwise"></i> Active Orders
                        </a>
                        <a class="nav-link" href="{{ url_for('order_history') }}">
                            <i class="bi bi-clock-history"></i> Order History
                        </a>
                        <a class="nav-link" href="{{ url_for('earnings') }}">
                            <i class="bi bi-currency-dollar"></i> Earnings & Payroll
                        </a>
                        <a class="nav-link" href="{{ url_for('system') }}">
                            <i class="bi bi-gear"></i> System Management
                        </a>
                    </nav>
                </div>
            </div>
            {% endblock %}

            <div class="col-md-9 col-lg-10">
                <div class="content-wrapper">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    {% block extra_js %}{% endblock %}
</body>

</html>