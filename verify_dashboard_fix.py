#!/usr/bin/env python
"""
Quick verification script for the dashboard template fix
"""

import os
import sys
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🔧 VERIFYING DASHBOARD TEMPLATE FIX")
    print("=" * 40)
    
    # Check 1: Verify template doesn't contain moment()
    print("\n1. Checking template for moment() function...")
    try:
        with open('templates/dashboard.html', 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        if 'moment()' in template_content:
            print("❌ Template still contains moment() function")
            return False
        else:
            print("✅ moment() function removed from template")
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False
    
    # Check 2: Verify template uses current_date variable
    print("\n2. Checking for current_date variable...")
    if '{{ current_date }}' in template_content:
        print("✅ Template uses {{ current_date }} variable")
    else:
        print("❌ Template missing {{ current_date }} variable")
        return False
    
    # Check 3: Test date formatting
    print("\n3. Testing date formatting...")
    try:
        current_date = datetime.now().strftime("%B %d, %Y")
        print(f"✅ Date format works: '{current_date}'")
    except Exception as e:
        print(f"❌ Date formatting error: {e}")
        return False
    
    # Check 4: Verify Flask route passes current_date
    print("\n4. Checking Flask route...")
    try:
        with open('web_management.py', 'r', encoding='utf-8') as f:
            route_content = f.read()
        
        if 'current_date=current_date' in route_content:
            print("✅ Flask route passes current_date to template")
        else:
            print("❌ Flask route missing current_date parameter")
            return False
    except Exception as e:
        print(f"❌ Error reading Flask route: {e}")
        return False
    
    # Check 5: Test template compilation
    print("\n5. Testing template compilation...")
    try:
        from web_management import app
        
        with app.app_context():
            from flask import render_template
            
            # Test template with sample data
            test_stats = {
                'total_personnel': 5,
                'active_personnel': 4,
                'completed_today': 10,
                'in_progress_today': 3,
                'pending_today': 2,
                'total_daily_orders': 15
            }
            
            test_date = datetime.now().strftime("%B %d, %Y")
            
            # This should not raise a Jinja2 error
            rendered = render_template('dashboard.html', 
                                     stats=test_stats, 
                                     current_date=test_date,
                                     user={'username': 'test'})
            
            if f"Daily Dashboard - {test_date}" in rendered:
                print("✅ Template renders correctly with date")
            else:
                print("❌ Template doesn't render date correctly")
                return False
                
    except Exception as e:
        print(f"❌ Template compilation error: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 ALL CHECKS PASSED!")
    print("\n✅ Dashboard Template Fix Summary:")
    print("• Removed invalid moment() function")
    print("• Added proper {{ current_date }} variable")
    print("• Updated Flask route to pass current_date")
    print("• Template compiles without Jinja2 errors")
    print("• Date displays in format: Month DD, YYYY")
    print("\n🚀 Dashboard should now load successfully!")
    print("\nNext steps:")
    print("1. Restart the web server: python web_management.py")
    print("2. Access dashboard at: http://localhost:5000")
    print("3. Login with: admin / admin123")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ SUCCESS: Dashboard template fix is complete!")
    else:
        print(f"\n❌ FAILURE: Dashboard template fix needs attention!")
    
    sys.exit(0 if success else 1)
