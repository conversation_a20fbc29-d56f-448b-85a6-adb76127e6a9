{% extends "base.html" %}

{% block title %}Personnel Management - Wiz-Aroma Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        <i class="bi bi-people"></i> Personnel Management
        {% if last_updated %}
        <small class="text-muted fs-6">Last updated: {{ last_updated[:19] }}</small>
        {% endif %}
    </h1>
    <div>
        <button type="button" class="btn btn-outline-secondary me-2" onclick="refreshPersonnelData()">
            <i class="bi bi-arrow-clockwise"></i> Refresh Data
        </button>
        <a href="{{ url_for('add_personnel') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Add New Personnel
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ personnel|length }}</h3>
                <p class="mb-0"><i class="bi bi-people"></i> Total Personnel</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ personnel.values()|selectattr('status', 'equalto', 'active')|list|length }}</h3>
                <p class="mb-0"><i class="bi bi-person-check"></i> Active Personnel</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ personnel.values()|rejectattr('status', 'equalto', 'active')|list|length }}</h3>
                <p class="mb-0"><i class="bi bi-person-x"></i> Inactive Personnel</p>
            </div>
        </div>
    </div>
</div>

<!-- Personnel Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-table"></i> Delivery Personnel List
        </h5>
    </div>
    <div class="card-body">
        {% if personnel %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Telegram ID</th>
                        <th>Phone Number</th>
                        <th>Status</th>
                        <th>Added Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for telegram_id, person in personnel.items() %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle p-2 me-2">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                                <strong>{{ person.name }}</strong>
                            </div>
                        </td>
                        <td>
                            <code>{{ person.telegram_id }}</code>
                        </td>
                        <td>
                            {% if person.phone_number %}
                            <span class="text-primary">
                                <i class="bi bi-telephone"></i> {{ person.phone_number }}
                            </span>
                            {% else %}
                            <span class="text-muted">
                                <i class="bi bi-dash"></i> Not provided
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if person.status == 'active' %}
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle"></i> Active
                            </span>
                            {% else %}
                            <span class="badge bg-secondary">
                                <i class="bi bi-x-circle"></i> {{ person.status|title }}
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if person.added_date %}
                            {{ person.added_date[:10] }}
                            {% else %}
                            <span class="text-muted">Unknown</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('edit_personnel', personnel_id=person.personnel_id) }}"
                                    class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-info"
                                    onclick="testAuthorization('{{ person.telegram_id }}')"
                                    title="Test bot authorization">
                                    <i class="bi bi-shield-check"></i> Test Auth
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                    onclick="confirmDelete('{{ person.personnel_id }}', '{{ person.name }}')">
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h4 class="mt-3">No Personnel Found</h4>
            <p class="text-muted">Start by adding your first delivery personnel.</p>
            <a href="{{ url_for('add_personnel') }}" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> Add First Personnel
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-warning"></i> Confirm Deletion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteName"></strong>?</p>
                <p class="text-muted">This action will:</p>
                <ul class="text-muted">
                    <li>Remove the personnel from the delivery system</li>
                    <li>Delete their earnings records</li>
                    <li>Remove their assignment history</li>
                </ul>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Delete Personnel
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(personnelId, name) {
        document.getElementById('deleteName').textContent = name;
        document.getElementById('deleteForm').action = '/personnel/delete/' + personnelId;

        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    // Real-time data refresh function
    function refreshPersonnelData() {
        console.log('Refreshing personnel data...');

        // Show loading indicator
        const refreshBtn = document.querySelector('button[onclick="refreshPersonnelData()"]');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Refreshing...';
        refreshBtn.disabled = true;

        fetch('/api/personnel/refresh')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Personnel data refreshed:', data);
                    showAlert('Personnel data refreshed successfully', 'success');
                    // Reload page to show updated data
                    setTimeout(() => location.reload(), 1000);
                } else {
                    console.error('Failed to refresh data:', data.error);
                    showAlert('Failed to refresh data: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error refreshing data:', error);
                showAlert('Error refreshing data: ' + error.message, 'danger');
            })
            .finally(() => {
                // Restore button
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            });
    }

    function showAlert(message, type) {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of content
        const content = document.querySelector('.container-fluid');
        content.insertBefore(alert, content.firstChild);

        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }

    function testAuthorization(telegramId) {
        console.log('Testing authorization for:', telegramId);

        fetch(`/api/personnel/test-authorization/${telegramId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Authorization test result:', data);

                let message = `Authorization Test for ${telegramId}:\n\n`;
                message += `✅ Is Authorized: ${data.is_authorized}\n`;
                message += `📋 In Authorized List: ${data.in_authorized_list}\n`;
                message += `🔢 Total Authorized IDs: ${data.authorized_ids_list.length}\n`;

                if (data.personnel_data) {
                    message += `👤 Personnel Status: ${data.personnel_data.status}\n`;
                    message += `📅 Added Date: ${data.personnel_data.added_date}\n`;
                } else {
                    message += `❌ Personnel data not found in Firebase\n`;
                }

                alert(message);
            })
            .catch(error => {
                console.error('Error testing authorization:', error);
                alert('Error testing authorization: ' + error.message);
            });
    }

    // Auto-refresh every 60 seconds (increased for better performance)
    setInterval(refreshPersonnelData, 60000);

    // Add CSS for spinning animation
    const style = document.createElement('style');
    style.textContent = `
        .spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}