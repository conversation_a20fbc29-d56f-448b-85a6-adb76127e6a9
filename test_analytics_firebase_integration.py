#!/usr/bin/env python
"""
Test script to verify Analytics tab Firebase integration
Tests real-time data accuracy and time-based filtering
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, delete_data, initialize_firebase
from src.config import logger

def test_analytics_firebase_integration():
    """Test analytics Firebase data integration"""
    print("📊 Testing Analytics Firebase Integration...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Test all time periods
        periods = ['daily', 'weekly', 'monthly', 'all_time']
        
        for period in periods:
            stats, daily_breakdown = calculate_accurate_analytics_for_period(period)
            
            if isinstance(stats, dict) and 'period' in stats:
                print(f"✅ {period.title()} analytics: {stats.get('total_orders', 0)} orders")
                print(f"   - Completed: {stats.get('completed_orders', 0)}")
                print(f"   - In Progress: {stats.get('in_progress_orders', 0)}")
                print(f"   - Pending: {stats.get('pending_orders', 0)}")
                print(f"   - Completion Rate: {stats.get('completion_rate', 0)}%")
                print(f"   - Earnings: {stats.get('total_earnings', 0):.2f} Birr")
            else:
                print(f"❌ {period.title()} analytics failed")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics Firebase integration test failed: {e}")
        return False

def test_analytics_time_filtering():
    """Test analytics time period filtering accuracy"""
    print("\n📅 Testing Analytics Time Filtering...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Get analytics for different periods
        daily_stats, _ = calculate_accurate_analytics_for_period('daily')
        weekly_stats, _ = calculate_accurate_analytics_for_period('weekly')
        monthly_stats, _ = calculate_accurate_analytics_for_period('monthly')
        all_time_stats, _ = calculate_accurate_analytics_for_period('all_time')
        
        # Validate logical relationships
        if (daily_stats['total_orders'] <= weekly_stats['total_orders'] <= 
            monthly_stats['total_orders'] <= all_time_stats['total_orders']):
            print("✅ Time filtering logic is correct (daily ≤ weekly ≤ monthly ≤ all_time)")
        else:
            print("❌ Time filtering logic is incorrect")
            print(f"Daily: {daily_stats['total_orders']}, Weekly: {weekly_stats['total_orders']}")
            print(f"Monthly: {monthly_stats['total_orders']}, All Time: {all_time_stats['total_orders']}")
            return False
        
        # Check date ranges
        for period, stats in [('daily', daily_stats), ('weekly', weekly_stats), ('monthly', monthly_stats)]:
            if stats.get('start_date') and stats.get('end_date'):
                print(f"✅ {period.title()} period: {stats['start_date']} to {stats['end_date']}")
            else:
                print(f"⚠️  {period.title()} period missing date range")
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics time filtering test failed: {e}")
        return False

def test_analytics_daily_breakdown():
    """Test analytics daily breakdown calculation"""
    print("\n📈 Testing Analytics Daily Breakdown...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Test daily breakdown for different periods
        periods = ['weekly', 'monthly']
        
        for period in periods:
            stats, daily_breakdown = calculate_accurate_analytics_for_period(period)
            
            if isinstance(daily_breakdown, dict):
                print(f"✅ {period.title()} breakdown: {len(daily_breakdown)} days")
                
                # Validate breakdown structure
                for date, data in daily_breakdown.items():
                    required_fields = ['completed', 'in_progress', 'pending', 'total']
                    if all(field in data for field in required_fields):
                        continue
                    else:
                        print(f"❌ Invalid breakdown structure for {date}")
                        return False
                
                # Show sample data
                if daily_breakdown:
                    sample_date = list(daily_breakdown.keys())[0]
                    sample_data = daily_breakdown[sample_date]
                    print(f"   Sample ({sample_date}): {sample_data}")
            else:
                print(f"❌ {period.title()} breakdown is not a dictionary")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics daily breakdown test failed: {e}")
        return False

def test_analytics_route_integration():
    """Test analytics route integration"""
    print("\n🌐 Testing Analytics Route Integration...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            # Test analytics route with different periods
            periods = ['daily', 'weekly', 'monthly', 'all_time']
            
            for period in periods:
                response = client.get(f'/analytics?period={period}')
                if response.status_code == 302:  # Redirect to login
                    print(f"✅ Analytics route with {period} period works")
                else:
                    print(f"❌ Analytics route with {period} period failed: {response.status_code}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics route integration test failed: {e}")
        return False

def test_analytics_data_consistency():
    """Test analytics data consistency with dashboard"""
    print("\n🔍 Testing Analytics-Dashboard Data Consistency...")
    
    try:
        from web_management import (
            calculate_accurate_analytics_for_period,
            calculate_accurate_daily_statistics
        )
        
        # Get daily analytics and dashboard stats
        analytics_daily, _ = calculate_accurate_analytics_for_period('daily')
        dashboard_daily = calculate_accurate_daily_statistics()
        
        # Compare key metrics
        analytics_completed = analytics_daily.get('completed_orders', 0)
        dashboard_completed = dashboard_daily.get('completed_today', 0)
        
        analytics_in_progress = analytics_daily.get('in_progress_orders', 0)
        dashboard_in_progress = dashboard_daily.get('in_progress_today', 0)
        
        analytics_pending = analytics_daily.get('pending_orders', 0)
        dashboard_pending = dashboard_daily.get('pending_today', 0)
        
        if (analytics_completed == dashboard_completed and
            analytics_in_progress == dashboard_in_progress and
            analytics_pending == dashboard_pending):
            print("✅ Analytics and Dashboard data are consistent")
            print(f"   Completed: {analytics_completed}")
            print(f"   In Progress: {analytics_in_progress}")
            print(f"   Pending: {analytics_pending}")
        else:
            print("❌ Analytics and Dashboard data are inconsistent")
            print(f"Analytics - Completed: {analytics_completed}, In Progress: {analytics_in_progress}, Pending: {analytics_pending}")
            print(f"Dashboard - Completed: {dashboard_completed}, In Progress: {dashboard_in_progress}, Pending: {dashboard_pending}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics-Dashboard consistency test failed: {e}")
        return False

def test_analytics_earnings_calculation():
    """Test analytics earnings calculation accuracy"""
    print("\n💰 Testing Analytics Earnings Calculation...")
    
    try:
        from web_management import get_real_time_firebase_data, calculate_accurate_analytics_for_period
        
        # Get Firebase data directly
        firebase_data = get_real_time_firebase_data()
        if not firebase_data:
            print("⚠️  No Firebase data available for earnings test")
            return True
        
        # Calculate expected earnings manually
        expected_earnings = 0.0
        today = datetime.now().date().isoformat()
        
        for order_data in firebase_data['completed_orders'].values():
            order_date = order_data.get('completion_date') or order_data.get('order_date', '')
            if order_date and order_date.startswith(today):
                delivery_fee = order_data.get('delivery_fee', 0)
                if isinstance(delivery_fee, (int, float)):
                    expected_earnings += delivery_fee * 0.5  # 50% share
        
        # Get analytics earnings
        daily_stats, _ = calculate_accurate_analytics_for_period('daily')
        analytics_earnings = daily_stats.get('total_earnings', 0.0)
        
        if abs(expected_earnings - analytics_earnings) < 0.01:  # Allow small floating point differences
            print(f"✅ Earnings calculation is accurate: {analytics_earnings:.2f} Birr")
        else:
            print(f"❌ Earnings calculation mismatch:")
            print(f"   Expected: {expected_earnings:.2f} Birr")
            print(f"   Analytics: {analytics_earnings:.2f} Birr")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics earnings calculation test failed: {e}")
        return False

def test_analytics_personnel_integration():
    """Test analytics personnel data integration"""
    print("\n👥 Testing Analytics Personnel Integration...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period, get_accurate_personnel_statistics
        
        # Get personnel stats from both sources
        analytics_stats, _ = calculate_accurate_analytics_for_period('daily')
        personnel_stats = get_accurate_personnel_statistics()
        
        # Compare personnel data
        if (analytics_stats.get('total_personnel') == personnel_stats.get('total_personnel') and
            analytics_stats.get('active_personnel') == personnel_stats.get('active_personnel')):
            print("✅ Analytics personnel data is consistent")
            print(f"   Total Personnel: {analytics_stats.get('total_personnel', 0)}")
            print(f"   Active Personnel: {analytics_stats.get('active_personnel', 0)}")
        else:
            print("❌ Analytics personnel data is inconsistent")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Analytics personnel integration test failed: {e}")
        return False

def main():
    """Run all analytics Firebase integration tests"""
    print("🧪 ANALYTICS FIREBASE INTEGRATION VERIFICATION")
    print("=" * 60)
    
    tests = [
        ("Analytics Firebase Integration", test_analytics_firebase_integration),
        ("Analytics Time Filtering", test_analytics_time_filtering),
        ("Analytics Daily Breakdown", test_analytics_daily_breakdown),
        ("Analytics Route Integration", test_analytics_route_integration),
        ("Analytics-Dashboard Consistency", test_analytics_data_consistency),
        ("Analytics Earnings Calculation", test_analytics_earnings_calculation),
        ("Analytics Personnel Integration", test_analytics_personnel_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL ANALYTICS FIREBASE INTEGRATION TESTS PASSED!")
        print("\n✅ Verified Features:")
        print("• Real-time Firebase data integration in Analytics")
        print("• Accurate time period filtering (daily/weekly/monthly/all-time)")
        print("• Proper daily breakdown calculation for charts")
        print("• Data consistency between Analytics and Dashboard")
        print("• Accurate earnings calculation from delivery fees")
        print("• Personnel data integration and consistency")
        print("\n🚀 Analytics tab displays 100% accurate Firebase data!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("❌ Analytics Firebase integration needs attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}: Analytics Firebase integration {'verified' if success else 'needs work'}")
    sys.exit(0 if success else 1)
