{% extends "base.html" %}

{% block title %}Earnings & Payroll - Wiz-Aroma Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        <i class="bi bi-currency-dollar"></i> Earnings & Payroll
    </h1>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" onclick="refreshEarnings()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportPayroll()">
            <i class="bi bi-download"></i> Export Payroll
        </button>
        <button type="button" class="btn btn-outline-warning" onclick="resetWeeklyEarnings()">
            <i class="bi bi-arrow-clockwise"></i> Reset Weekly
        </button>
    </div>
</div>

<!-- Earnings Summary -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ "%.2f"|format(earnings.values()|sum(attribute='total_earnings') or 0) }}</h3>
                <p class="mb-0"><i class="bi bi-currency-dollar"></i> Total Earnings (Birr)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ "%.2f"|format(earnings.values()|sum(attribute='weekly_earnings') or 0) }}</h3>
                <p class="mb-0"><i class="bi bi-calendar-week"></i> Weekly Earnings (Birr)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ earnings.values()|sum(attribute='deliveries_completed') or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-box-seam"></i> Total Deliveries</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ earnings.values()|selectattr('status', 'equalto', 'active')|list|length }}</h3>
                <p class="mb-0"><i class="bi bi-people"></i> Active Personnel</p>
            </div>
        </div>
    </div>
</div>

<!-- Payroll Period Selector -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="mb-0">
                    <i class="bi bi-calendar-range"></i> Payroll Period
                </h6>
                <small class="text-muted">Weekly earnings reset every Monday</small>
            </div>
            <div class="col-md-6">
                <div class="btn-group w-100" role="group">
                    <input type="radio" class="btn-check" name="payrollPeriod" id="currentWeek" value="current" checked>
                    <label class="btn btn-outline-primary" for="currentWeek">Current Week</label>
                    
                    <input type="radio" class="btn-check" name="payrollPeriod" id="lastWeek" value="last">
                    <label class="btn btn-outline-primary" for="lastWeek">Last Week</label>
                    
                    <input type="radio" class="btn-check" name="payrollPeriod" id="allTime" value="alltime">
                    <label class="btn btn-outline-primary" for="allTime">All Time</label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Earnings Chart -->
<div class="row mb-4">
    <div class="col-md-8 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-bar-chart"></i> Earnings by Personnel</h5>
            </div>
            <div class="card-body">
                <canvas id="earningsChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pie-chart"></i> Earnings Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="earningsDistributionChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Personnel Earnings Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-table"></i> Personnel Earnings Details
        </h5>
    </div>
    <div class="card-body">
        {% if earnings %}
        <div class="table-responsive">
            <table class="table table-hover" id="earningsTable">
                <thead>
                    <tr>
                        <th>Personnel</th>
                        <th>Telegram ID</th>
                        <th>Status</th>
                        <th>Deliveries</th>
                        <th>Weekly Earnings</th>
                        <th>Total Earnings</th>
                        <th>Avg per Delivery</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for personnel_id, data in earnings.items() %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle p-2 me-2">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                                <strong>{{ data.name }}</strong>
                            </div>
                        </td>
                        <td>
                            <code>{{ data.telegram_id }}</code>
                        </td>
                        <td>
                            {% if data.status == 'active' %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle"></i> Active
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="bi bi-pause-circle"></i> {{ data.status|title }}
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ data.deliveries_completed }}</span>
                        </td>
                        <td>
                            <strong class="text-success">{{ "%.2f"|format(data.weekly_earnings) }} Birr</strong>
                        </td>
                        <td>
                            <strong class="text-primary">{{ "%.2f"|format(data.total_earnings) }} Birr</strong>
                        </td>
                        <td>
                            {% if data.deliveries_completed > 0 %}
                                {{ "%.2f"|format(data.total_earnings / data.deliveries_completed) }} Birr
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="viewEarningsHistory('{{ personnel_id }}')">
                                    <i class="bi bi-clock-history"></i> History
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" 
                                        onclick="generatePayslip('{{ personnel_id }}')">
                                    <i class="bi bi-receipt"></i> Payslip
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-currency-dollar display-1 text-muted"></i>
            <h4 class="mt-3">No Earnings Data</h4>
            <p class="text-muted">Earnings will appear here once delivery personnel complete orders.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Earnings History Modal -->
<div class="modal fade" id="earningsHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clock-history"></i> Earnings History
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="earningsHistoryContent">
                <!-- Earnings history will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Earnings data
const earningsData = {{ earnings|tojson }};

// Initialize charts
let earningsChart, earningsDistributionChart;

// Earnings by Personnel Chart
const earningsCtx = document.getElementById('earningsChart').getContext('2d');
const personnelNames = Object.values(earningsData).map(p => p.name);
const weeklyEarnings = Object.values(earningsData).map(p => p.weekly_earnings);
const totalEarnings = Object.values(earningsData).map(p => p.total_earnings);

earningsChart = new Chart(earningsCtx, {
    type: 'bar',
    data: {
        labels: personnelNames,
        datasets: [{
            label: 'Weekly Earnings (Birr)',
            data: weeklyEarnings,
            backgroundColor: '#4CAF50',
            borderRadius: 8
        }, {
            label: 'Total Earnings (Birr)',
            data: totalEarnings,
            backgroundColor: '#2196F3',
            borderRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Earnings Distribution Chart
const earningsDistCtx = document.getElementById('earningsDistributionChart').getContext('2d');
earningsDistributionChart = new Chart(earningsDistCtx, {
    type: 'doughnut',
    data: {
        labels: personnelNames,
        datasets: [{
            data: totalEarnings,
            backgroundColor: [
                '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
                '#00BCD4', '#8BC34A', '#FFC107', '#795548', '#607D8B'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function viewEarningsHistory(personnelId) {
    const personnel = Object.values(earningsData).find(p => 
        p.telegram_id.toString() === personnelId.split('_')[2]
    );
    
    if (!personnel) {
        alert('Personnel not found');
        return;
    }
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Personnel Information</h6>
                <p><strong>Name:</strong> ${personnel.name}</p>
                <p><strong>Telegram ID:</strong> ${personnel.telegram_id}</p>
                <p><strong>Status:</strong> ${personnel.status}</p>
            </div>
            <div class="col-md-6">
                <h6>Earnings Summary</h6>
                <p><strong>Total Earnings:</strong> ${personnel.total_earnings.toFixed(2)} Birr</p>
                <p><strong>Weekly Earnings:</strong> ${personnel.weekly_earnings.toFixed(2)} Birr</p>
                <p><strong>Deliveries:</strong> ${personnel.deliveries_completed}</p>
            </div>
        </div>
        <hr>
        <h6>Recent Earnings (Last 7 days)</h6>
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i> 
            Detailed earnings history will be implemented with transaction tracking.
        </div>
    `;
    
    document.getElementById('earningsHistoryContent').innerHTML = content;
    
    const modal = new bootstrap.Modal(document.getElementById('earningsHistoryModal'));
    modal.show();
}

function generatePayslip(personnelId) {
    alert('Payslip generation will create PDF payslips for personnel');
}

function refreshEarnings() {
    const refreshBtn = document.querySelector('button[onclick="refreshEarnings()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function exportPayroll() {
    alert('Export functionality will generate payroll reports in CSV/PDF format');
}

function resetWeeklyEarnings() {
    if (confirm('Are you sure you want to reset weekly earnings for all personnel? This action cannot be undone.')) {
        alert('Weekly earnings reset functionality will be implemented');
    }
}

// Payroll period change handler
document.querySelectorAll('input[name="payrollPeriod"]').forEach(radio => {
    radio.addEventListener('change', function() {
        updatePayrollView(this.value);
    });
});

function updatePayrollView(period) {
    console.log('Updating payroll view for period:', period);
    // In production, this would filter the data based on the selected period
}

// Auto-refresh every 2 minutes
setInterval(refreshEarnings, 120000);
</script>
{% endblock %}
