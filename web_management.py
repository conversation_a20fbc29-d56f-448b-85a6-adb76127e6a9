#!/usr/bin/env python
"""
Wiz-Aroma Web Management Interface
Flask-based web application to replace the Telegram management bot
with a professional web interface while preserving all functionality.
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from functools import wraps
import json

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from werkzeug.exceptions import BadRequest

# Import existing Wiz-Aroma modules
from src.firebase_db import get_data, set_data, delete_data, update_data, initialize_firebase
from src.config import logger
from src.utils.helpers import is_admin

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 'wiz-aroma-management-secret-key-2024')
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=8)  # 8-hour sessions

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access the management interface.'
login_manager.login_message_category = 'info'

# Initialize Firebase
initialize_firebase()

# ============================================================================
# USER AUTHENTICATION SYSTEM
# ============================================================================

class User(UserMixin):
    """User class for Flask-Login"""
    def __init__(self, user_id, username, role='admin'):
        self.id = user_id
        self.username = username
        self.role = role
        self._is_active = True
        self._is_authenticated = True
        self._is_anonymous = False

    def get_id(self):
        return str(self.id)

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value):
        self._is_active = bool(value)

    @property
    def is_authenticated(self):
        return self._is_authenticated

    @is_authenticated.setter
    def is_authenticated(self, value):
        self._is_authenticated = bool(value)

    @property
    def is_anonymous(self):
        return self._is_anonymous

    @is_anonymous.setter
    def is_anonymous(self, value):
        self._is_anonymous = bool(value)

# Default admin credentials (should be changed in production)
DEFAULT_ADMIN_USERS = {
    'admin': {
        'password_hash': generate_password_hash('admin123'),  # Change this!
        'role': 'admin',
        'telegram_id': 7729984017  # Link to existing admin Telegram ID
    }
}

@login_manager.user_loader
def load_user(user_id):
    """Load user for Flask-Login"""
    try:
        logger.debug(f"Loading user: {user_id}")

        # Try to get user from Firebase first
        try:
            user_data = get_data(f"web_admin_users/{user_id}")
            if user_data:
                logger.debug(f"Found Firebase user: {user_id}")
                return User(user_id, user_data.get('username'), user_data.get('role', 'admin'))
        except Exception as e:
            logger.warning(f"Firebase user lookup failed for {user_id}: {e}")

        # Fallback to default admin users
        for username, data in DEFAULT_ADMIN_USERS.items():
            if username == user_id:
                logger.debug(f"Found default admin user: {user_id}")
                return User(user_id, username, data.get('role', 'admin'))

        logger.warning(f"User not found: {user_id}")
        return None
    except Exception as e:
        logger.error(f"Error loading user {user_id}: {e}")
        return None

def authenticate_user(username, password):
    """Authenticate user credentials"""
    try:
        logger.info(f"Authentication attempt for user: {username}")

        # Check Firebase users first
        try:
            users_data = get_data("web_admin_users") or {}
            for user_id, user_data in users_data.items():
                if user_data.get('username') == username:
                    if check_password_hash(user_data.get('password_hash', ''), password):
                        logger.info(f"Firebase user authentication successful for: {username}")
                        return User(user_id, username, user_data.get('role', 'admin'))
        except Exception as e:
            logger.warning(f"Firebase user check failed: {e}")

        # Check default admin users
        if username in DEFAULT_ADMIN_USERS:
            user_data = DEFAULT_ADMIN_USERS[username]
            try:
                if check_password_hash(user_data['password_hash'], password):
                    logger.info(f"Default admin authentication successful for: {username}")
                    return User(username, username, user_data.get('role', 'admin'))
                else:
                    logger.warning(f"Password verification failed for default admin: {username}")
            except Exception as e:
                logger.error(f"Error checking default admin password: {e}")

        logger.warning(f"Authentication failed for user: {username}")
        return None
    except Exception as e:
        logger.error(f"Error authenticating user {username}: {e}")
        return None

# ============================================================================
# UTILITY FUNCTIONS FROM MANAGEMENT BOT
# ============================================================================

def get_authorized_delivery_personnel():
    """Get authorized delivery personnel from Firebase with enhanced error handling"""
    try:
        logger.debug("Fetching delivery personnel from Firebase...")

        # Test Firebase connection first
        if not initialize_firebase():
            logger.error("Firebase not initialized")
            return {}

        personnel_data = get_data("delivery_personnel") or {}
        logger.info(f"Retrieved {len(personnel_data)} personnel records from Firebase")

        authorized_personnel = {}

        for personnel_id, data in personnel_data.items():
            try:
                if not isinstance(data, dict):
                    logger.warning(f"Invalid data format for personnel {personnel_id}: {type(data)}")
                    continue

                telegram_id = data.get('telegram_id')
                name = data.get('name', 'Unknown')
                status = data.get('status', 'unknown')

                if not telegram_id:
                    logger.warning(f"Personnel {personnel_id} missing telegram_id")
                    continue

                # Include all personnel (not just active) for management interface
                authorized_personnel[str(telegram_id)] = {
                    'name': name,
                    'telegram_id': telegram_id,
                    'status': status,
                    'added_date': data.get('added_date', ''),
                    'added_by': data.get('added_by', ''),
                    'last_modified': data.get('last_modified', ''),
                    'personnel_id': personnel_id,
                    'phone_number': data.get('phone_number', '')  # Add phone number support
                }

                logger.debug(f"Added personnel: {name} (ID: {telegram_id}, Status: {status})")

            except Exception as e:
                logger.error(f"Error processing personnel {personnel_id}: {e}")
                continue

        logger.info(f"Successfully processed {len(authorized_personnel)} personnel records")
        return authorized_personnel

    except Exception as e:
        logger.error(f"Error getting authorized delivery personnel: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {}

def add_authorized_delivery_personnel(telegram_id, name, added_by_admin_id, phone_number=None):
    """Add new personnel to authorized delivery list with enhanced validation"""
    try:
        logger.info(f"Adding delivery personnel: {name} (ID: {telegram_id})")

        # Validate Telegram ID
        if not isinstance(telegram_id, (int, str)) or not str(telegram_id).isdigit():
            raise ValueError(f"Invalid Telegram ID: {telegram_id}")

        telegram_id = int(telegram_id)

        # Validate name
        if not name or len(name.strip()) < 2:
            raise ValueError("Name must be at least 2 characters long")

        # Validate phone number if provided
        if phone_number:
            from src.utils.helpers import is_valid_phone
            if not is_valid_phone(phone_number):
                raise ValueError("Invalid phone number format. Please use Ethiopian phone number format.")

        # Test Firebase connection
        if not initialize_firebase():
            raise Exception("Firebase connection failed")

        # Get current authorized personnel
        authorized_personnel = get_authorized_delivery_personnel()

        # Check if already exists
        if str(telegram_id) in authorized_personnel:
            return False, "Personnel with this Telegram ID already exists"

        # Create unique personnel ID
        personnel_id = f"delivery_personnel_{telegram_id}"

        # Create personnel record
        personnel_record = {
            'telegram_id': telegram_id,
            'name': name.strip(),
            'status': 'active',
            'added_date': datetime.utcnow().isoformat(),
            'added_by': str(added_by_admin_id),
            'phone_number': phone_number.strip() if phone_number else ''
        }

        # Save to Firebase
        logger.debug(f"Saving personnel record to Firebase: {personnel_id}")
        set_data(f"delivery_personnel/{personnel_id}", personnel_record)

        # Initialize earnings record
        earnings_record = {
            'total_earnings': 0,
            'weekly_earnings': 0,
            'last_reset': datetime.utcnow().isoformat(),
            'deliveries_completed': 0
        }
        logger.debug(f"Saving earnings record to Firebase: {personnel_id}")
        set_data(f"delivery_personnel_earnings/{personnel_id}", earnings_record)

        # Verify the data was saved
        saved_data = get_data(f"delivery_personnel/{personnel_id}")
        if not saved_data:
            raise Exception("Failed to save personnel data to Firebase")

        # Clear authorization cache to ensure immediate authorization
        try:
            from src.bots.delivery_bot import clear_authorization_cache
            clear_authorization_cache()
            logger.info("Cleared delivery bot authorization cache for immediate effect")
        except Exception as cache_error:
            logger.warning(f"Failed to clear authorization cache: {cache_error}")

        # Verify authorization works immediately
        try:
            from src.bots.delivery_bot import is_authorized
            auth_check = is_authorized(telegram_id)
            logger.info(f"Authorization verification for {telegram_id}: {auth_check}")
            if not auth_check:
                logger.warning(f"WARNING: Newly added personnel {telegram_id} is not immediately authorized!")
        except Exception as auth_error:
            logger.warning(f"Failed to verify authorization: {auth_error}")

        logger.info(f"Successfully added delivery personnel: {name} (ID: {telegram_id})")
        return True, "Personnel added successfully and authorized for bot access"

    except ValueError as e:
        logger.warning(f"Validation error adding personnel: {e}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Error adding delivery personnel: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False, f"Error adding personnel: {str(e)}"

def get_analytics_data():
    """Get analytics data (same as management bot)"""
    try:
        data = {
            'completed_orders': get_data("completed_orders") or {},
            'confirmed_orders': get_data("confirmed_orders") or {},
            'assignments': get_data("delivery_personnel_assignments") or {},
            'earnings': get_data("delivery_personnel_earnings") or {},
            'personnel': get_data("delivery_personnel") or {}
        }
        return data
    except Exception as e:
        logger.error(f"Error getting analytics data: {e}")
        return {}

# ============================================================================
# ROUTE HANDLERS
# ============================================================================

def get_real_time_firebase_data():
    """Get real-time data directly from Firebase Firestore collections"""
    try:
        logger.info("Fetching real-time data from Firebase Firestore...")

        # Test Firebase connection first
        if not initialize_firebase():
            logger.error("Firebase initialization failed")
            return None

        # Get all Firebase collections
        firebase_data = {
            'delivery_personnel': get_data("delivery_personnel") or {},
            'delivery_personnel_earnings': get_data("delivery_personnel_earnings") or {},
            'completed_orders': get_data("completed_orders") or {},
            'confirmed_orders': get_data("confirmed_orders") or {},
            'pending_admin_reviews': get_data("pending_admin_reviews") or {},
            'current_orders': get_data("current_orders") or {},
            'order_assignments': get_data("order_assignments") or {}
        }

        logger.info(f"Firebase data retrieved: {len(firebase_data['delivery_personnel'])} personnel, "
                   f"{len(firebase_data['completed_orders'])} completed orders, "
                   f"{len(firebase_data['confirmed_orders'])} confirmed orders")

        return firebase_data

    except Exception as e:
        logger.error(f"Error getting real-time Firebase data: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None

def calculate_accurate_daily_statistics():
    """Calculate accurate daily statistics from Firebase data"""
    try:
        from datetime import datetime, timedelta

        # Get real-time Firebase data
        firebase_data = get_real_time_firebase_data()
        if not firebase_data:
            logger.warning("No Firebase data available, returning zero statistics")
            return {
                'total_orders_today': 0,
                'completed_today': 0,
                'in_progress_today': 0,
                'pending_today': 0,
                'completion_rate': 0.0,
                'total_earnings_today': 0.0
            }

        today = datetime.now().date()
        today_str = today.isoformat()

        logger.info(f"Calculating statistics for date: {today_str}")

        # Initialize counters
        stats = {
            'total_orders_today': 0,
            'completed_today': 0,
            'in_progress_today': 0,
            'pending_today': 0,
            'completion_rate': 0.0,
            'total_earnings_today': 0.0
        }

        # Count completed orders for today
        for order_id, order_data in firebase_data['completed_orders'].items():
            order_date = order_data.get('completion_date') or order_data.get('order_date', '')
            if order_date and order_date.startswith(today_str):
                stats['completed_today'] += 1
                stats['total_orders_today'] += 1

                # Calculate earnings from delivery fees
                delivery_fee = order_data.get('delivery_fee', 0)
                if isinstance(delivery_fee, (int, float)):
                    stats['total_earnings_today'] += delivery_fee * 0.5  # 50% to delivery personnel

        # Count confirmed orders (in-progress) for today
        for order_id, order_data in firebase_data['confirmed_orders'].items():
            order_date = order_data.get('order_date', '')
            if order_date and order_date.startswith(today_str):
                # Check if this order is not already completed
                if order_id not in firebase_data['completed_orders']:
                    stats['in_progress_today'] += 1
                    stats['total_orders_today'] += 1

        # Count pending orders for today
        for order_id, order_data in firebase_data['pending_admin_reviews'].items():
            order_date = order_data.get('order_date', '')
            if order_date and order_date.startswith(today_str):
                stats['pending_today'] += 1
                stats['total_orders_today'] += 1

        # Count current orders (new orders) for today
        for order_id, order_data in firebase_data['current_orders'].items():
            order_date = order_data.get('order_date', '')
            if order_date and order_date.startswith(today_str):
                # Check if not already counted in other categories
                if (order_id not in firebase_data['completed_orders'] and
                    order_id not in firebase_data['confirmed_orders'] and
                    order_id not in firebase_data['pending_admin_reviews']):
                    stats['pending_today'] += 1
                    stats['total_orders_today'] += 1

        # Calculate completion rate
        if stats['total_orders_today'] > 0:
            stats['completion_rate'] = round((stats['completed_today'] / stats['total_orders_today']) * 100, 1)

        logger.info(f"Daily statistics calculated: {stats}")
        return stats

    except Exception as e:
        logger.error(f"Error calculating daily statistics: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'total_orders_today': 0,
            'completed_today': 0,
            'in_progress_today': 0,
            'pending_today': 0,
            'completion_rate': 0.0,
            'total_earnings_today': 0.0
        }

def get_accurate_personnel_statistics():
    """Get accurate personnel statistics from Firebase"""
    try:
        logger.info("Calculating personnel statistics from Firebase...")

        # Get real-time Firebase data
        firebase_data = get_real_time_firebase_data()
        if not firebase_data:
            logger.warning("No Firebase data available for personnel statistics")
            return {
                'total_personnel': 0,
                'active_personnel': 0,
                'inactive_personnel': 0
            }

        personnel_data = firebase_data['delivery_personnel']

        stats = {
            'total_personnel': len(personnel_data),
            'active_personnel': 0,
            'inactive_personnel': 0
        }

        # Count personnel by status
        for personnel_id, person_data in personnel_data.items():
            status = person_data.get('status', 'unknown')
            if status == 'active':
                stats['active_personnel'] += 1
            else:
                stats['inactive_personnel'] += 1

        logger.info(f"Personnel statistics: {stats}")
        return stats

    except Exception as e:
        logger.error(f"Error getting personnel statistics: {e}")
        return {
            'total_personnel': 0,
            'active_personnel': 0,
            'inactive_personnel': 0
        }

@app.route('/')
@login_required
def dashboard():
    """Main dashboard page - displays accurate daily data from Firebase"""
    try:
        # Get current date for display
        from datetime import datetime
        current_date = datetime.now().strftime("%B %d, %Y")

        logger.info("Loading dashboard with real-time Firebase data...")

        # Get accurate daily statistics from Firebase
        daily_stats = calculate_accurate_daily_statistics()

        # Get accurate personnel statistics from Firebase
        personnel_stats = get_accurate_personnel_statistics()

        # Combine all statistics for dashboard
        stats = {
            # Personnel statistics
            'total_personnel': personnel_stats['total_personnel'],
            'active_personnel': personnel_stats['active_personnel'],
            'inactive_personnel': personnel_stats['inactive_personnel'],

            # Daily order statistics
            'total_daily_orders': daily_stats['total_orders_today'],
            'completed_today': daily_stats['completed_today'],
            'in_progress_today': daily_stats['in_progress_today'],
            'pending_today': daily_stats['pending_today'],

            # Performance metrics
            'completion_rate': daily_stats['completion_rate'],
            'total_earnings_today': daily_stats['total_earnings_today']
        }

        logger.info(f"Dashboard loaded with accurate stats: {stats}")

        # Add data freshness indicator
        stats['last_updated'] = datetime.now().strftime("%H:%M:%S")
        stats['data_source'] = 'Firebase Firestore (Real-time)'

        return render_template('dashboard.html', stats=stats, user=current_user, current_date=current_date)

    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        import traceback
        logger.error(f"Dashboard error traceback: {traceback.format_exc()}")
        flash('Error loading dashboard data from Firebase', 'error')

        # Provide fallback data and date even in error case
        from datetime import datetime
        current_date = datetime.now().strftime("%B %d, %Y")
        fallback_stats = {
            'total_personnel': 0,
            'active_personnel': 0,
            'inactive_personnel': 0,
            'total_daily_orders': 0,
            'completed_today': 0,
            'in_progress_today': 0,
            'pending_today': 0,
            'completion_rate': 0.0,
            'total_earnings_today': 0.0,
            'last_updated': datetime.now().strftime("%H:%M:%S"),
            'data_source': 'Error - Using fallback data'
        }

        return render_template('dashboard.html', stats=fallback_stats, user=current_user, current_date=current_date)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        try:
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '')
            remember = bool(request.form.get('remember'))

            logger.info(f"Login attempt from IP: {request.remote_addr}, Username: {username}")

            if not username or not password:
                flash('Please enter both username and password', 'error')
                return render_template('login.html')

            user = authenticate_user(username, password)
            if user:
                try:
                    login_user(user, remember=remember)
                    next_page = request.args.get('next')
                    flash(f'Welcome back, {user.username}!', 'success')
                    logger.info(f"Successful login for user: {username}")
                    return redirect(next_page) if next_page else redirect(url_for('dashboard'))
                except Exception as e:
                    logger.error(f"Error during login_user for {username}: {e}")
                    flash('Login system error. Please try again.', 'error')
            else:
                flash('Invalid username or password', 'error')
                logger.warning(f"Failed login attempt for user: {username}")

        except Exception as e:
            logger.error(f"Login route error: {e}")
            flash('An error occurred during login. Please try again.', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Logout user"""
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('login'))

# ============================================================================
# PERSONNEL MANAGEMENT ROUTES
# ============================================================================

@app.route('/personnel')
@login_required
def personnel():
    """Personnel management page with real-time data"""
    try:
        logger.info("Loading personnel management page with real-time data")

        # Force fresh data from Firebase (no caching)
        personnel_data = get_authorized_delivery_personnel()

        logger.info(f"Loaded {len(personnel_data)} personnel records for display")

        # Add real-time status information
        current_time = datetime.utcnow().isoformat()

        return render_template('personnel.html',
                             personnel=personnel_data,
                             last_updated=current_time,
                             total_personnel=len(personnel_data))
    except Exception as e:
        logger.error(f"Error loading personnel page: {e}")
        import traceback
        logger.error(f"Personnel page error traceback: {traceback.format_exc()}")
        flash('Error loading personnel data', 'error')
        return render_template('personnel.html', personnel={}, last_updated=None, total_personnel=0)

@app.route('/personnel/add', methods=['GET', 'POST'])
@login_required
def add_personnel():
    """Add new delivery personnel with phone number support"""
    if request.method == 'POST':
        try:
            telegram_id = request.form.get('telegram_id', '').strip()
            name = request.form.get('name', '').strip()
            phone_number = request.form.get('phone_number', '').strip()

            logger.info(f"Personnel add request: ID={telegram_id}, Name={name}, Phone={phone_number}")

            if not telegram_id or not name:
                flash('Please provide both Telegram ID and name', 'error')
                return render_template('add_personnel.html')

            # Validate Telegram ID format
            if not telegram_id.isdigit():
                flash('Telegram ID must be a number', 'error')
                return render_template('add_personnel.html')

            # Validate phone number if provided
            if phone_number:
                from src.utils.helpers import is_valid_phone
                if not is_valid_phone(phone_number):
                    flash('Invalid phone number format. Please use Ethiopian phone number format (e.g., 09xxxxxxxx)', 'error')
                    return render_template('add_personnel.html')

            # Add personnel using enhanced function
            success, message = add_authorized_delivery_personnel(
                telegram_id, name, current_user.id, phone_number
            )

            if success:
                flash(f'Successfully added {name} (ID: {telegram_id})', 'success')
                logger.info(f"Personnel added successfully: {name} (ID: {telegram_id})")
                return redirect(url_for('personnel'))
            else:
                flash(message, 'error')
                logger.warning(f"Failed to add personnel: {message}")
                return render_template('add_personnel.html')

        except Exception as e:
            logger.error(f"Error adding personnel: {e}")
            flash('Error adding personnel. Please check the logs for details.', 'error')
            return render_template('add_personnel.html')

    return render_template('add_personnel.html')

@app.route('/personnel/edit/<personnel_id>', methods=['GET', 'POST'])
@login_required
def edit_personnel(personnel_id):
    """Edit delivery personnel with phone number support"""
    try:
        logger.info(f"Editing personnel: {personnel_id}")

        # Get personnel data
        personnel_data = get_data(f"delivery_personnel/{personnel_id}")
        if not personnel_data:
            flash('Personnel not found', 'error')
            logger.warning(f"Personnel not found: {personnel_id}")
            return redirect(url_for('personnel'))

        if request.method == 'POST':
            name = request.form.get('name', '').strip()
            status = request.form.get('status', 'active')
            phone_number = request.form.get('phone_number', '').strip()

            logger.info(f"Personnel edit request: Name={name}, Status={status}, Phone={phone_number}")

            if not name:
                flash('Please provide a name', 'error')
                return render_template('edit_personnel.html', personnel=personnel_data, personnel_id=personnel_id)

            # Validate phone number if provided
            if phone_number:
                from src.utils.helpers import is_valid_phone
                if not is_valid_phone(phone_number):
                    flash('Invalid phone number format. Please use Ethiopian phone number format (e.g., 09xxxxxxxx)', 'error')
                    return render_template('edit_personnel.html', personnel=personnel_data, personnel_id=personnel_id)

            # Update personnel data
            personnel_data['name'] = name
            personnel_data['status'] = status
            personnel_data['phone_number'] = phone_number
            personnel_data['last_modified'] = datetime.utcnow().isoformat()
            personnel_data['modified_by'] = current_user.id

            # Save to Firebase
            set_data(f"delivery_personnel/{personnel_id}", personnel_data)

            # Verify the update
            updated_data = get_data(f"delivery_personnel/{personnel_id}")
            if updated_data and updated_data.get('name') == name:
                flash(f'Successfully updated {name}', 'success')
                logger.info(f"Personnel updated successfully: {name}")
            else:
                flash('Update may not have been saved properly', 'warning')
                logger.warning(f"Personnel update verification failed for: {personnel_id}")

            return redirect(url_for('personnel'))

        return render_template('edit_personnel.html', personnel=personnel_data, personnel_id=personnel_id)

    except Exception as e:
        logger.error(f"Error editing personnel {personnel_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        flash('Error editing personnel. Please check the logs for details.', 'error')
        return redirect(url_for('personnel'))

@app.route('/personnel/delete/<personnel_id>', methods=['POST'])
@login_required
def delete_personnel(personnel_id):
    """Delete delivery personnel with comprehensive cleanup"""
    try:
        logger.info(f"Starting deletion process for personnel: {personnel_id}")

        # Get personnel data first
        personnel_data = get_data(f"delivery_personnel/{personnel_id}")
        if not personnel_data:
            logger.warning(f"Personnel {personnel_id} not found in Firebase")
            flash('Personnel not found', 'error')
            return redirect(url_for('personnel'))

        name = personnel_data.get('name', 'Unknown')
        telegram_id = personnel_data.get('telegram_id', 'Unknown')

        logger.info(f"Deleting personnel: {name} (ID: {telegram_id}, Key: {personnel_id})")

        # Delete from all Firebase collections
        collections_to_clean = [
            f"delivery_personnel/{personnel_id}",
            f"delivery_personnel_earnings/{personnel_id}",
            f"delivery_personnel_assignments/{personnel_id}",
            f"order_assignments/{personnel_id}",  # Additional cleanup
        ]

        deletion_results = []
        for collection_path in collections_to_clean:
            try:
                # Check if data exists before deletion
                existing_data = get_data(collection_path)
                if existing_data:
                    delete_data(collection_path)
                    deletion_results.append(f"✅ Deleted {collection_path}")
                    logger.info(f"Successfully deleted {collection_path}")
                else:
                    deletion_results.append(f"ℹ️ No data found in {collection_path}")
                    logger.info(f"No data found in {collection_path}")
            except Exception as delete_error:
                deletion_results.append(f"❌ Failed to delete {collection_path}: {delete_error}")
                logger.error(f"Failed to delete {collection_path}: {delete_error}")

        # Clear authorization cache to ensure immediate effect
        try:
            from src.bots.delivery_bot import clear_authorization_cache
            clear_authorization_cache()
            logger.info("Cleared delivery bot authorization cache")
        except Exception as cache_error:
            logger.warning(f"Failed to clear authorization cache: {cache_error}")

        # Verify deletion
        verification_data = get_data(f"delivery_personnel/{personnel_id}")
        if verification_data:
            logger.error(f"DELETION FAILED: Personnel {personnel_id} still exists in Firebase")
            flash(f'Error: Failed to delete {name} from Firebase', 'error')
        else:
            logger.info(f"DELETION VERIFIED: Personnel {personnel_id} successfully removed")
            flash(f'Successfully removed {name} (ID: {telegram_id})', 'success')

            # Log detailed deletion results
            logger.info(f"Deletion summary for {name}:")
            for result in deletion_results:
                logger.info(f"  {result}")

    except Exception as e:
        logger.error(f"Error deleting personnel {personnel_id}: {e}")
        import traceback
        logger.error(f"Deletion error traceback: {traceback.format_exc()}")
        flash('Error deleting personnel. Check logs for details.', 'error')

    return redirect(url_for('personnel'))

# ============================================================================
# ANALYTICS ROUTES
# ============================================================================

def calculate_accurate_analytics_for_period(period='daily'):
    """Calculate accurate analytics data from Firebase for specific time period"""
    try:
        from datetime import datetime, timedelta

        logger.info(f"Calculating analytics for period: {period}")

        # Get real-time Firebase data
        firebase_data = get_real_time_firebase_data()
        if not firebase_data:
            logger.warning("No Firebase data available for analytics")
            return {
                'period': period,
                'total_orders': 0,
                'completed_orders': 0,
                'in_progress_orders': 0,
                'pending_orders': 0,
                'total_earnings': 0.0,
                'completion_rate': 0.0
            }, {}

        logger.info(f"Firebase collections found: {list(firebase_data.keys())}")
        for collection, data in firebase_data.items():
            logger.info(f"  {collection}: {len(data)} records")

        # Additional debugging for weekly period
        if period == 'weekly':
            logger.info(f"WEEKLY PERIOD DEBUG:")
            logger.info(f"  firebase_data type: {type(firebase_data)}")
            logger.info(f"  completed_orders type: {type(firebase_data.get('completed_orders', 'MISSING'))}")
            logger.info(f"  completed_orders length: {len(firebase_data.get('completed_orders', {}))}")

            if firebase_data.get('completed_orders'):
                logger.info(f"  First few completed order IDs: {list(firebase_data['completed_orders'].keys())[:3]}")
            else:
                logger.warning(f"  completed_orders is empty or None!")

        # Get time range for filtering
        now = datetime.now()
        if period == 'daily':
            # For daily, include all of today
            start_date = now.date()
            end_date = now.date()
        elif period == 'weekly':
            # For weekly, start from Monday of current week
            current_weekday = now.weekday()  # Monday = 0, Sunday = 6
            days_since_monday = current_weekday
            start_date = (now - timedelta(days=days_since_monday)).date()  # This Monday
            end_date = now.date()  # Today
        elif period == 'monthly':
            # For monthly, include last 30 days including today
            start_date = (now - timedelta(days=29)).date()  # Changed from 30 to 29 to include today
            end_date = now.date()
        else:  # all_time
            start_date = None
            end_date = None

        logger.info(f"Date range for {period}: {start_date} to {end_date}")
        logger.info(f"Current date: {now.date()}, Current time: {now.time()}")

        # Special logging for weekly period
        if period == 'weekly':
            logger.info(f"WEEKLY PERIOD ANALYSIS:")
            logger.info(f"  Current datetime: {now}")
            logger.info(f"  Start date (6 days ago): {start_date}")
            logger.info(f"  End date (today): {end_date}")
            logger.info(f"  Days in range: {(end_date - start_date).days + 1}")

            # Show each day in the weekly range
            current_date = start_date
            day_num = 1
            while current_date <= end_date:
                logger.info(f"    Day {day_num}: {current_date}")
                current_date += timedelta(days=1)
                day_num += 1

        # Initialize counters
        stats = {
            'period': period,
            'total_orders': 0,
            'completed_orders': 0,
            'in_progress_orders': 0,
            'pending_orders': 0,
            'total_earnings': 0.0,
            'completion_rate': 0.0,
            'start_date': start_date.isoformat() if start_date else None,
            'end_date': end_date.isoformat() if end_date else None
        }

        # Process completed orders
        completed_count = 0
        total_earnings = 0.0

        # Debug the completed orders data
        completed_orders_data = firebase_data.get('completed_orders', {})
        logger.info(f"Processing completed orders - count: {len(completed_orders_data)}")

        if period == 'weekly':
            logger.info(f"WEEKLY COMPLETED ORDERS DEBUG:")
            logger.info(f"  completed_orders_data type: {type(completed_orders_data)}")
            logger.info(f"  completed_orders_data keys: {list(completed_orders_data.keys()) if completed_orders_data else 'EMPTY'}")

        # Special tracking for weekly period
        weekly_included_orders = []
        weekly_excluded_orders = []

        # Log all order dates for debugging - ALWAYS for weekly period
        if period == 'weekly':
            logger.info(f"WEEKLY ORDER DATE ANALYSIS:")
            logger.info(f"  completed_orders_data available: {completed_orders_data is not None}")
            logger.info(f"  completed_orders_data length: {len(completed_orders_data) if completed_orders_data else 0}")

            if completed_orders_data:
                for order_id, order_data in completed_orders_data.items():
                    order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
                    logger.info(f"  Order {order_id}: raw_date='{order_date_str}', all_keys={list(order_data.keys())}")
            else:
                logger.warning(f"  WEEKLY ORDER DATE ANALYSIS SKIPPED - completed_orders_data is empty or None")
                logger.warning(f"  This explains why weekly analytics shows 0 - no orders to process!")

        for order_id, order_data in completed_orders_data.items():
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')

            # For all_time, include all orders regardless of date
            if period == 'all_time':
                completed_count += 1
                stats['total_orders'] += 1

                # Calculate earnings
                delivery_fee = order_data.get('delivery_fee', 0)
                if isinstance(delivery_fee, (int, float)):
                    total_earnings += delivery_fee * 0.5  # 50% to delivery personnel

                logger.debug(f"All-time order {order_id}: fee={delivery_fee}")
                continue

            # For time-based periods, check date
            if order_date_str:
                try:
                    # Parse date string
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    logger.info(f"Checking order {order_id}: date={order_date}, range={start_date} to {end_date}")

                    # Check if order falls within the period
                    if start_date <= order_date <= end_date:
                        completed_count += 1
                        stats['total_orders'] += 1

                        # Calculate earnings
                        delivery_fee = order_data.get('delivery_fee', 0)
                        if isinstance(delivery_fee, (int, float)):
                            total_earnings += delivery_fee * 0.5  # 50% to delivery personnel

                        logger.info(f"✅ INCLUDED order {order_id} ({order_date}): fee={delivery_fee}")

                        # Track for weekly analysis
                        if period == 'weekly':
                            weekly_included_orders.append({
                                'id': order_id,
                                'date': order_date,
                                'fee': delivery_fee,
                                'type': 'completed'
                            })
                    else:
                        logger.info(f"❌ EXCLUDED order {order_id} ({order_date}): outside range")

                        # Track excluded orders for weekly analysis
                        if period == 'weekly':
                            weekly_excluded_orders.append({
                                'id': order_id,
                                'date': order_date,
                                'reason': 'outside_date_range'
                            })

                except Exception as e:
                    logger.warning(f"Failed to parse date '{order_date_str}' for order {order_id}: {e}")
                    # Include orders with unparseable dates in all_time only
                    if period == 'all_time':
                        completed_count += 1
                        stats['total_orders'] += 1

                    # Track for weekly analysis
                    if period == 'weekly':
                        weekly_excluded_orders.append({
                            'id': order_id,
                            'date': order_date_str,
                            'reason': f'date_parsing_error: {e}'
                        })
            else:
                logger.warning(f"Order {order_id} has no date information")
                # Include orders without dates in all_time only
                if period == 'all_time':
                    completed_count += 1
                    stats['total_orders'] += 1

        stats['completed_orders'] = completed_count
        stats['total_earnings'] = total_earnings

        logger.info(f"Completed orders processed: {completed_count}, earnings: {total_earnings:.2f}")

        # Process in-progress orders (confirmed but not completed)
        in_progress_count = 0

        logger.info(f"Processing {len(firebase_data['confirmed_orders'])} confirmed orders")

        for order_id, order_data in firebase_data['confirmed_orders'].items():
            # Skip if already completed
            if order_id in firebase_data['completed_orders']:
                logger.debug(f"Skipping confirmed order {order_id} - already completed")
                continue

            # For all_time, include all orders regardless of date
            if period == 'all_time':
                in_progress_count += 1
                stats['total_orders'] += 1
                logger.debug(f"All-time in-progress order {order_id}")
                continue

            order_date_str = order_data.get('order_date', '')
            if order_date_str:
                try:
                    # Parse date string
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if start_date <= order_date <= end_date:
                        in_progress_count += 1
                        stats['total_orders'] += 1
                        logger.debug(f"Period in-progress order {order_id} ({order_date})")
                except Exception as e:
                    logger.warning(f"Failed to parse date '{order_date_str}' for confirmed order {order_id}: {e}")
                    if period == 'all_time':
                        in_progress_count += 1
                        stats['total_orders'] += 1
            else:
                logger.warning(f"Confirmed order {order_id} has no date information")
                if period == 'all_time':
                    in_progress_count += 1
                    stats['total_orders'] += 1

        stats['in_progress_orders'] = in_progress_count

        logger.info(f"In-progress orders processed: {in_progress_count}")

        # Process pending orders
        pending_count = 0

        logger.info(f"Processing {len(firebase_data['pending_admin_reviews'])} pending admin reviews")

        # From pending admin reviews
        for order_id, order_data in firebase_data['pending_admin_reviews'].items():
            # For all_time, include all orders regardless of date
            if period == 'all_time':
                pending_count += 1
                stats['total_orders'] += 1
                logger.debug(f"All-time pending admin review {order_id}")
                continue

            order_date_str = order_data.get('order_date', '')
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if start_date <= order_date <= end_date:
                        pending_count += 1
                        stats['total_orders'] += 1
                        logger.debug(f"Period pending admin review {order_id} ({order_date})")
                except Exception as e:
                    logger.warning(f"Failed to parse date '{order_date_str}' for pending order {order_id}: {e}")
                    if period == 'all_time':
                        pending_count += 1
                        stats['total_orders'] += 1
            else:
                logger.warning(f"Pending admin review {order_id} has no date information")
                if period == 'all_time':
                    pending_count += 1
                    stats['total_orders'] += 1

        logger.info(f"Processing {len(firebase_data['current_orders'])} current orders")

        # From current orders (new orders)
        for order_id, order_data in firebase_data['current_orders'].items():
            # Skip if already counted in other categories
            if (order_id in firebase_data['completed_orders'] or
                order_id in firebase_data['confirmed_orders'] or
                order_id in firebase_data['pending_admin_reviews']):
                logger.debug(f"Skipping current order {order_id} - already counted in other category")
                continue

            # For all_time, include all orders regardless of date
            if period == 'all_time':
                pending_count += 1
                stats['total_orders'] += 1
                logger.debug(f"All-time current order {order_id}")
                continue

            order_date_str = order_data.get('order_date', '')
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if start_date <= order_date <= end_date:
                        pending_count += 1
                        stats['total_orders'] += 1
                        logger.debug(f"Period current order {order_id} ({order_date})")
                except Exception as e:
                    logger.warning(f"Failed to parse date '{order_date_str}' for current order {order_id}: {e}")
                    if period == 'all_time':
                        pending_count += 1
                        stats['total_orders'] += 1
            else:
                logger.warning(f"Current order {order_id} has no date information")
                if period == 'all_time':
                    pending_count += 1
                    stats['total_orders'] += 1

        stats['pending_orders'] = pending_count

        logger.info(f"Pending orders processed: {pending_count}")

        # Calculate completion rate
        if stats['total_orders'] > 0:
            stats['completion_rate'] = round((stats['completed_orders'] / stats['total_orders']) * 100, 1)

        # Get personnel statistics
        personnel_stats = get_accurate_personnel_statistics()
        stats.update(personnel_stats)

        # Calculate daily breakdown for charts
        daily_breakdown = calculate_accurate_daily_breakdown(firebase_data, period, start_date, end_date)

        # Special summary for weekly period
        if period == 'weekly':
            logger.info(f"WEEKLY ANALYTICS SUMMARY:")
            logger.info(f"  Date range: {start_date} to {end_date}")
            logger.info(f"  Total orders found: {stats['total_orders']}")
            logger.info(f"  Completed orders: {stats['completed_orders']}")
            logger.info(f"  In-progress orders: {stats['in_progress_orders']}")
            logger.info(f"  Pending orders: {stats['pending_orders']}")
            logger.info(f"  Total earnings: {stats['total_earnings']:.2f}")
            logger.info(f"  Completion rate: {stats['completion_rate']}%")
            logger.info(f"  Daily breakdown days: {len(daily_breakdown)}")

            if 'weekly_included_orders' in locals():
                logger.info(f"  Orders included in weekly: {len(weekly_included_orders)}")
                for order in weekly_included_orders[:5]:  # Show first 5
                    logger.info(f"    {order['id']}: {order['date']} ({order['type']})")

            if 'weekly_excluded_orders' in locals():
                logger.info(f"  Orders excluded from weekly: {len(weekly_excluded_orders)}")
                for order in weekly_excluded_orders[:3]:  # Show first 3
                    logger.info(f"    {order['id']}: {order['date']} ({order['reason']})")

        logger.info(f"Analytics calculated for {period}: {stats}")
        return stats, daily_breakdown

    except Exception as e:
        logger.error(f"Error calculating analytics for period {period}: {e}")
        import traceback
        logger.error(f"Analytics error traceback: {traceback.format_exc()}")
        return {}, {}

def calculate_accurate_daily_breakdown(firebase_data, period, start_date, end_date):
    """Calculate accurate daily breakdown for charts from Firebase data"""
    try:
        from datetime import datetime, timedelta

        breakdown = {}

        logger.info(f"Calculating daily breakdown for {period}: {start_date} to {end_date}")

        # Process completed orders
        for order_id, order_data in firebase_data['completed_orders'].items():
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    # Check if within period
                    if start_date is None or (start_date <= order_date <= end_date):
                        date_key = order_date.isoformat()
                        if date_key not in breakdown:
                            breakdown[date_key] = {
                                'completed': 0,
                                'in_progress': 0,
                                'pending': 0,
                                'total': 0
                            }
                        breakdown[date_key]['completed'] += 1
                        breakdown[date_key]['total'] += 1
                except:
                    pass

        # Process in-progress orders (confirmed but not completed)
        for order_id, order_data in firebase_data['confirmed_orders'].items():
            if order_id in firebase_data['completed_orders']:
                continue  # Skip completed orders

            order_date_str = order_data.get('order_date', '')
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if start_date is None or (start_date <= order_date <= end_date):
                        date_key = order_date.isoformat()
                        if date_key not in breakdown:
                            breakdown[date_key] = {
                                'completed': 0,
                                'in_progress': 0,
                                'pending': 0,
                                'total': 0
                            }
                        breakdown[date_key]['in_progress'] += 1
                        breakdown[date_key]['total'] += 1
                except:
                    pass

        # Process pending orders
        all_pending_orders = {}
        all_pending_orders.update(firebase_data['pending_admin_reviews'])

        # Add current orders that aren't in other categories
        for order_id, order_data in firebase_data['current_orders'].items():
            if (order_id not in firebase_data['completed_orders'] and
                order_id not in firebase_data['confirmed_orders'] and
                order_id not in firebase_data['pending_admin_reviews']):
                all_pending_orders[order_id] = order_data

        for order_id, order_data in all_pending_orders.items():
            order_date_str = order_data.get('order_date', '')
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if start_date is None or (start_date <= order_date <= end_date):
                        date_key = order_date.isoformat()
                        if date_key not in breakdown:
                            breakdown[date_key] = {
                                'completed': 0,
                                'in_progress': 0,
                                'pending': 0,
                                'total': 0
                            }
                        breakdown[date_key]['pending'] += 1
                        breakdown[date_key]['total'] += 1
                except:
                    pass

        logger.info(f"Daily breakdown calculated for {period}: {len(breakdown)} days")
        return breakdown

    except Exception as e:
        logger.error(f"Error calculating daily breakdown: {e}")
        return {}

@app.route('/analytics')
@login_required
def analytics():
    """Analytics dashboard page with accurate Firebase data and time period filtering"""
    try:
        period = request.args.get('period', 'daily')

        logger.info(f"Loading analytics for period: {period}")

        # Special handling for weekly period
        if period == 'weekly':
            logger.info("WEEKLY ANALYTICS REQUEST - Starting detailed analysis")

            # Test Firebase connection first
            firebase_data = get_real_time_firebase_data()
            if firebase_data:
                logger.info(f"Firebase data available for weekly analysis:")
                for collection, data in firebase_data.items():
                    logger.info(f"  {collection}: {len(data)} records")
            else:
                logger.error("No Firebase data available for weekly analytics")

        # Calculate accurate analytics from Firebase for the selected period
        stats, daily_breakdown = calculate_accurate_analytics_for_period(period)

        # Add metadata for data source tracking
        from datetime import datetime
        stats['last_updated'] = datetime.now().strftime("%H:%M:%S")
        stats['data_source'] = 'Firebase Firestore (Real-time)'
        stats['current_date'] = datetime.now().strftime("%B %d, %Y")

        # Get additional Firebase data for validation
        firebase_data = get_real_time_firebase_data()

        # Create analytics data structure for template compatibility
        analytics_data = {
            'firebase_collections': {
                'delivery_personnel': len(firebase_data.get('delivery_personnel', {})) if firebase_data else 0,
                'completed_orders': len(firebase_data.get('completed_orders', {})) if firebase_data else 0,
                'confirmed_orders': len(firebase_data.get('confirmed_orders', {})) if firebase_data else 0,
                'pending_admin_reviews': len(firebase_data.get('pending_admin_reviews', {})) if firebase_data else 0,
                'current_orders': len(firebase_data.get('current_orders', {})) if firebase_data else 0
            },
            'period_info': {
                'selected': period,
                'start_date': stats.get('start_date'),
                'end_date': stats.get('end_date')
            }
        }

        logger.info(f"Analytics loaded successfully for {period}: {stats}")

        return render_template('analytics.html',
                             stats=stats,
                             analytics_data=analytics_data,
                             daily_breakdown=daily_breakdown,
                             current_period=period)

    except Exception as e:
        logger.error(f"Error loading analytics: {e}")
        import traceback
        logger.error(f"Analytics error traceback: {traceback.format_exc()}")
        flash('Error loading analytics data from Firebase', 'error')

        # Provide fallback data
        from datetime import datetime
        fallback_stats = {
            'period': period,
            'total_orders': 0,
            'completed_orders': 0,
            'in_progress_orders': 0,
            'pending_orders': 0,
            'total_earnings': 0.0,
            'completion_rate': 0.0,
            'total_personnel': 0,
            'active_personnel': 0,
            'last_updated': datetime.now().strftime("%H:%M:%S"),
            'data_source': 'Error - Using fallback data',
            'current_date': datetime.now().strftime("%B %d, %Y")
        }

        return render_template('analytics.html',
                             stats=fallback_stats,
                             analytics_data={},
                             daily_breakdown={},
                             current_period=period)

# ============================================================================
# ORDER MANAGEMENT ROUTES
# ============================================================================

def get_order_status_details():
    """Get detailed order status information from Firebase"""
    try:
        # Get all order-related data
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        pending_orders = get_data("pending_admin_reviews") or {}
        current_orders = get_data("current_orders") or {}

        # Categorize orders by status
        orders_by_status = {
            'in_progress': {},
            'completed': {},
            'pending': {},
            'confirmed': {}
        }

        # Process completed orders
        for order_id, order_data in completed_orders.items():
            order_data['status'] = 'completed'
            order_data['status_display'] = 'Completed'
            order_data['status_class'] = 'success'
            orders_by_status['completed'][order_id] = order_data

        # Process confirmed orders (in progress)
        for order_id, order_data in confirmed_orders.items():
            if order_id not in completed_orders:  # Not yet completed
                order_data['status'] = 'in_progress'
                order_data['status_display'] = 'In Progress'
                order_data['status_class'] = 'info'
                orders_by_status['in_progress'][order_id] = order_data

        # Process pending orders
        for order_id, order_data in pending_orders.items():
            order_data['status'] = 'pending'
            order_data['status_display'] = 'Pending Review'
            order_data['status_class'] = 'warning'
            orders_by_status['pending'][order_id] = order_data

        # Process current orders (newly placed)
        for order_id, order_data in current_orders.items():
            if order_id not in confirmed_orders and order_id not in completed_orders:
                order_data['status'] = 'new'
                order_data['status_display'] = 'New Order'
                order_data['status_class'] = 'primary'
                orders_by_status['pending'][order_id] = order_data

        return orders_by_status

    except Exception as e:
        logger.error(f"Error getting order status details: {e}")
        return {'in_progress': {}, 'completed': {}, 'pending': {}, 'confirmed': {}}

def filter_orders_by_period(orders, period='daily'):
    """Filter orders by time period"""
    try:
        from datetime import datetime, timedelta

        now = datetime.now()

        if period == 'daily':
            start_date = now.date()
            end_date = start_date
        elif period == 'weekly':
            start_date = (now - timedelta(days=7)).date()
            end_date = now.date()
        elif period == 'monthly':
            start_date = (now - timedelta(days=30)).date()
            end_date = now.date()
        else:  # all_time
            return orders

        filtered_orders = {}
        for order_id, order_data in orders.items():
            order_date_str = order_data.get('order_date') or order_data.get('completion_date', '')
            if order_date_str:
                try:
                    # Parse the date string
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if start_date <= order_date <= end_date:
                        filtered_orders[order_id] = order_data
                except:
                    # If date parsing fails, include the order
                    filtered_orders[order_id] = order_data
            else:
                # If no date, include the order
                filtered_orders[order_id] = order_data

        return filtered_orders

    except Exception as e:
        logger.error(f"Error filtering orders by period {period}: {e}")
        return orders

@app.route('/orders')
@login_required
def orders():
    """Order management page - shows only in-progress/incomplete orders"""
    try:
        period = request.args.get('period', 'daily')
        orders_by_status = get_order_status_details()

        # Get ONLY in-progress orders (exclude completed and pending)
        in_progress_orders = orders_by_status['in_progress']

        # Filter by time period
        filtered_orders = filter_orders_by_period(in_progress_orders, period)

        # Calculate statistics for the filtered period
        all_orders_filtered = {}
        for status_orders in orders_by_status.values():
            all_orders_filtered.update(filter_orders_by_period(status_orders, period))

        stats = {
            'total_active': len(filtered_orders),
            'in_progress': len(filtered_orders),
            'pending': len(filter_orders_by_period(orders_by_status['pending'], period)),
            'completed': len(filter_orders_by_period(orders_by_status['completed'], period)),
            'period': period
        }

        return render_template('orders.html', orders=filtered_orders, stats=stats, page_type='active', current_period=period)

    except Exception as e:
        logger.error(f"Error loading orders: {e}")
        flash('Error loading orders data', 'error')
        return render_template('orders.html', orders={}, stats={}, page_type='active', current_period='daily')

@app.route('/orders/history')
@login_required
def order_history():
    """Order history page - shows only completed orders confirmed by customers"""
    try:
        period = request.args.get('period', 'daily')
        orders_by_status = get_order_status_details()

        # Get ONLY completed orders (confirmed by customers)
        completed_orders = orders_by_status['completed']

        # Filter by time period
        filtered_orders = filter_orders_by_period(completed_orders, period)

        # Calculate statistics for the filtered period
        from datetime import datetime, timedelta

        stats = {
            'total_completed': len(filtered_orders),
            'completed_today': 0,
            'completed_week': 0,
            'completed_month': 0,
            'period': period
        }

        # Calculate date-based statistics for all periods
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        for order_data in completed_orders.values():
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if order_date == today:
                        stats['completed_today'] += 1
                    if order_date >= week_ago:
                        stats['completed_week'] += 1
                    if order_date >= month_ago:
                        stats['completed_month'] += 1
                except:
                    pass

        return render_template('orders.html', orders=filtered_orders, stats=stats, page_type='history', current_period=period)

    except Exception as e:
        logger.error(f"Error loading order history: {e}")
        flash('Error loading order history data', 'error')
        return render_template('orders.html', orders={}, stats={}, page_type='history', current_period='daily')

@app.route('/api/orders/status')
@login_required
def api_order_status():
    """API endpoint for real-time order status updates"""
    try:
        orders_by_status = get_order_status_details()

        # Return summary statistics for real-time updates
        summary = {
            'in_progress_count': len(orders_by_status['in_progress']),
            'pending_count': len(orders_by_status['pending']),
            'completed_count': len(orders_by_status['completed']),
            'last_updated': datetime.utcnow().isoformat()
        }

        return jsonify(summary)

    except Exception as e:
        logger.error(f"Error getting order status API: {e}")
        return jsonify({'error': 'Failed to get order status'}), 500

@app.route('/api/dashboard/refresh')
@login_required
def api_dashboard_refresh():
    """API endpoint for refreshing dashboard data from Firebase"""
    try:
        logger.info("API dashboard refresh requested")

        # Get fresh data from Firebase
        daily_stats = calculate_accurate_daily_statistics()
        personnel_stats = get_accurate_personnel_statistics()

        # Combine statistics
        refresh_data = {
            # Personnel statistics
            'total_personnel': personnel_stats['total_personnel'],
            'active_personnel': personnel_stats['active_personnel'],
            'inactive_personnel': personnel_stats['inactive_personnel'],

            # Daily order statistics
            'total_daily_orders': daily_stats['total_orders_today'],
            'completed_today': daily_stats['completed_today'],
            'in_progress_today': daily_stats['in_progress_today'],
            'pending_today': daily_stats['pending_today'],

            # Performance metrics
            'completion_rate': daily_stats['completion_rate'],
            'total_earnings_today': daily_stats['total_earnings_today'],

            # Metadata
            'last_updated': datetime.now().strftime("%H:%M:%S"),
            'data_source': 'Firebase Firestore (Real-time)',
            'refresh_timestamp': datetime.now().isoformat()
        }

        logger.info(f"Dashboard refresh completed: {refresh_data}")
        return jsonify(refresh_data)

    except Exception as e:
        logger.error(f"Error refreshing dashboard data: {e}")
        import traceback
        logger.error(f"Refresh error traceback: {traceback.format_exc()}")

        return jsonify({
            'error': 'Failed to refresh dashboard data',
            'message': str(e),
            'last_updated': datetime.now().strftime("%H:%M:%S"),
            'data_source': 'Error - Refresh failed'
        }), 500

@app.route('/api/firebase/status')
@login_required
def api_firebase_status():
    """API endpoint to check Firebase connection status"""
    try:
        # Test Firebase connection
        firebase_data = get_real_time_firebase_data()

        if firebase_data:
            status = {
                'connected': True,
                'status': 'connected',
                'collections': {
                    'delivery_personnel': len(firebase_data.get('delivery_personnel', {})),
                    'completed_orders': len(firebase_data.get('completed_orders', {})),
                    'confirmed_orders': len(firebase_data.get('confirmed_orders', {})),
                    'pending_admin_reviews': len(firebase_data.get('pending_admin_reviews', {})),
                    'current_orders': len(firebase_data.get('current_orders', {}))
                },
                'last_checked': datetime.now().isoformat()
            }
        else:
            status = {
                'connected': False,
                'status': 'error',
                'error': 'Failed to retrieve Firebase data',
                'last_checked': datetime.now().isoformat()
            }

        return jsonify(status)

    except Exception as e:
        logger.error(f"Error checking Firebase status: {e}")
        return jsonify({
            'connected': False,
            'status': 'error',
            'error': str(e),
            'last_checked': datetime.now().isoformat()
        }), 500

@app.route('/api/analytics/debug')
@login_required
def api_analytics_debug():
    """Debug endpoint for analytics data"""
    try:
        period = request.args.get('period', 'daily')

        from datetime import datetime, timedelta

        # Get current date info
        now = datetime.now()
        current_date = now.date()
        current_time = now.time()

        # Get Firebase data
        firebase_data = get_real_time_firebase_data()

        # Calculate analytics
        stats, daily_breakdown = calculate_accurate_analytics_for_period(period)

        debug_info = {
            'period': period,
            'current_datetime': now.isoformat(),
            'current_date': current_date.isoformat(),
            'current_time': current_time.isoformat(),
            'firebase_data_available': firebase_data is not None,
            'firebase_collections': {
                'delivery_personnel': len(firebase_data.get('delivery_personnel', {})) if firebase_data else 0,
                'completed_orders': len(firebase_data.get('completed_orders', {})) if firebase_data else 0,
                'confirmed_orders': len(firebase_data.get('confirmed_orders', {})) if firebase_data else 0,
                'pending_admin_reviews': len(firebase_data.get('pending_admin_reviews', {})) if firebase_data else 0,
                'current_orders': len(firebase_data.get('current_orders', {})) if firebase_data else 0
            },
            'calculated_stats': stats,
            'daily_breakdown_days': len(daily_breakdown) if daily_breakdown else 0,
            'daily_breakdown_sample': dict(list(daily_breakdown.items())[:5]) if daily_breakdown else {},
            'sample_orders': {},
            'date_analysis': {}
        }

        # Add detailed date analysis
        if firebase_data:
            # Analyze all completed order dates
            completed_dates = []
            for order_id, order_data in firebase_data['completed_orders'].items():
                order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
                if order_date_str:
                    try:
                        if 'T' in order_date_str:
                            order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                        else:
                            order_date = datetime.fromisoformat(order_date_str).date()
                        completed_dates.append(order_date.isoformat())
                    except:
                        completed_dates.append(f"INVALID: {order_date_str}")

            debug_info['date_analysis'] = {
                'completed_order_dates': completed_dates,
                'unique_dates': list(set([d for d in completed_dates if not d.startswith('INVALID')])),
                'today_matches': [d for d in completed_dates if d == current_date.isoformat()],
                'period_range': {
                    'daily': f"{current_date.isoformat()} to {current_date.isoformat()}",
                    'weekly': f"{(now - timedelta(days=6)).date().isoformat()} to {current_date.isoformat()}",
                    'monthly': f"{(now - timedelta(days=29)).date().isoformat()} to {current_date.isoformat()}"
                }
            }

            # Sample completed orders with full details
            completed_sample = list(firebase_data['completed_orders'].items())[:5]
            debug_info['sample_orders']['completed'] = [
                {
                    'id': order_id,
                    'completion_date': order_data.get('completion_date'),
                    'order_date': order_data.get('order_date'),
                    'delivery_fee': order_data.get('delivery_fee'),
                    'all_fields': list(order_data.keys())
                }
                for order_id, order_data in completed_sample
            ]

            # Sample confirmed orders
            confirmed_sample = list(firebase_data['confirmed_orders'].items())[:3]
            debug_info['sample_orders']['confirmed'] = [
                {
                    'id': order_id,
                    'order_date': order_data.get('order_date'),
                    'all_fields': list(order_data.keys())
                }
                for order_id, order_data in confirmed_sample
            ]

        return jsonify(debug_info)

    except Exception as e:
        logger.error(f"Error in analytics debug: {e}")
        import traceback
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/analytics/weekly-sync-test')
@login_required
def api_weekly_sync_test():
    """Specific test endpoint for weekly data synchronization"""
    try:
        from datetime import datetime, timedelta

        # Get current time and weekly range
        now = datetime.now()
        weekly_start = (now - timedelta(days=6)).date()
        weekly_end = now.date()

        # Get Firebase data
        firebase_data = get_real_time_firebase_data()

        if not firebase_data:
            return jsonify({'error': 'No Firebase data available'}), 500

        # Analyze weekly data manually
        weekly_analysis = {
            'test_timestamp': now.isoformat(),
            'weekly_range': {
                'start_date': weekly_start.isoformat(),
                'end_date': weekly_end.isoformat(),
                'days_included': (weekly_end - weekly_start).days + 1
            },
            'firebase_raw_counts': {
                'completed_orders': len(firebase_data['completed_orders']),
                'confirmed_orders': len(firebase_data['confirmed_orders']),
                'pending_admin_reviews': len(firebase_data['pending_admin_reviews']),
                'current_orders': len(firebase_data['current_orders'])
            },
            'weekly_filtered_data': {
                'completed_in_range': [],
                'confirmed_in_range': [],
                'pending_in_range': [],
                'current_in_range': []
            },
            'date_analysis': {
                'all_completed_dates': [],
                'weekly_dates_found': [],
                'outside_range_dates': []
            }
        }

        # Process completed orders
        for order_id, order_data in firebase_data['completed_orders'].items():
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')

            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    weekly_analysis['date_analysis']['all_completed_dates'].append(order_date.isoformat())

                    if weekly_start <= order_date <= weekly_end:
                        weekly_analysis['weekly_filtered_data']['completed_in_range'].append({
                            'id': order_id,
                            'date': order_date.isoformat(),
                            'delivery_fee': order_data.get('delivery_fee', 0)
                        })
                        weekly_analysis['date_analysis']['weekly_dates_found'].append(order_date.isoformat())
                    else:
                        weekly_analysis['date_analysis']['outside_range_dates'].append(order_date.isoformat())

                except Exception as e:
                    weekly_analysis['date_analysis']['all_completed_dates'].append(f"INVALID: {order_date_str}")

        # Process confirmed orders
        for order_id, order_data in firebase_data['confirmed_orders'].items():
            if order_id in firebase_data['completed_orders']:
                continue  # Skip completed orders

            order_date_str = order_data.get('order_date', '')

            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()

                    if weekly_start <= order_date <= weekly_end:
                        weekly_analysis['weekly_filtered_data']['confirmed_in_range'].append({
                            'id': order_id,
                            'date': order_date.isoformat()
                        })

                except Exception as e:
                    pass

        # Calculate weekly totals
        weekly_analysis['weekly_totals'] = {
            'completed_orders': len(weekly_analysis['weekly_filtered_data']['completed_in_range']),
            'confirmed_orders': len(weekly_analysis['weekly_filtered_data']['confirmed_in_range']),
            'pending_orders': len(weekly_analysis['weekly_filtered_data']['pending_in_range']) + len(weekly_analysis['weekly_filtered_data']['current_in_range']),
            'total_orders': len(weekly_analysis['weekly_filtered_data']['completed_in_range']) + len(weekly_analysis['weekly_filtered_data']['confirmed_in_range']),
            'total_earnings': sum(order.get('delivery_fee', 0) * 0.5 for order in weekly_analysis['weekly_filtered_data']['completed_in_range'] if isinstance(order.get('delivery_fee', 0), (int, float)))
        }

        # Get function calculation for comparison
        function_stats, function_breakdown = calculate_accurate_analytics_for_period('weekly')

        weekly_analysis['function_calculation'] = function_stats
        weekly_analysis['function_breakdown_days'] = len(function_breakdown) if function_breakdown else 0

        # Compare manual vs function calculation
        weekly_analysis['comparison'] = {
            'manual_total': weekly_analysis['weekly_totals']['total_orders'],
            'function_total': function_stats.get('total_orders', 0),
            'manual_completed': weekly_analysis['weekly_totals']['completed_orders'],
            'function_completed': function_stats.get('completed_orders', 0),
            'manual_earnings': weekly_analysis['weekly_totals']['total_earnings'],
            'function_earnings': function_stats.get('total_earnings', 0),
            'calculations_match': (
                weekly_analysis['weekly_totals']['total_orders'] == function_stats.get('total_orders', 0) and
                weekly_analysis['weekly_totals']['completed_orders'] == function_stats.get('completed_orders', 0)
            )
        }

        return jsonify(weekly_analysis)

    except Exception as e:
        logger.error(f"Error in weekly sync test: {e}")
        import traceback
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/analytics/week-info')
@login_required
def api_week_info():
    """Get current week information"""
    try:
        from datetime import datetime, timedelta

        now = datetime.now()
        current_weekday = now.weekday()  # Monday = 0
        monday_this_week = (now - timedelta(days=current_weekday)).date()

        week_info = {
            'current_datetime': now.isoformat(),
            'current_date': now.date().isoformat(),
            'current_weekday': current_weekday,
            'current_weekday_name': now.strftime('%A'),
            'monday_this_week': monday_this_week.isoformat(),
            'week_range': {
                'start': monday_this_week.isoformat(),
                'end': now.date().isoformat()
            },
            'days_in_week_so_far': current_weekday + 1,
            'week_days': []
        }

        # Add each day of the week so far
        for i in range(current_weekday + 1):
            day_date = monday_this_week + timedelta(days=i)
            week_info['week_days'].append({
                'date': day_date.isoformat(),
                'day_name': day_date.strftime('%A'),
                'day_number': i
            })

        return jsonify(week_info)

    except Exception as e:
        logger.error(f"Error getting week info: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics/create-weekly-test-data')
@login_required
def api_create_weekly_test_data():
    """Create test data for current week to fix weekly analytics"""
    try:
        from datetime import datetime, timedelta

        now = datetime.now()
        current_weekday = now.weekday()  # Monday = 0
        monday_this_week = now - timedelta(days=current_weekday)

        created_orders = []

        # Create orders for each day from Monday to today
        for i in range(current_weekday + 1):  # From Monday to today
            order_date = monday_this_week + timedelta(days=i)
            day_name = order_date.strftime('%A')

            # Create completed order
            completed_order = {
                'order_id': f'weekly_test_completed_{i}',
                'completion_date': order_date.isoformat(),
                'order_date': order_date.isoformat(),
                'delivery_fee': 50.0 + (i * 10),
                'customer_id': f'weekly_test_customer_{i}',
                'status': 'completed',
                'restaurant_name': 'Weekly Test Restaurant',
                'delivery_address': f'Weekly Test Address {i}',
                'created_by': 'weekly_test_api',
                'created_at': datetime.now().isoformat()
            }

            order_key = f"weekly_test_completed_{i}"
            set_data(f"completed_orders/{order_key}", completed_order)
            created_orders.append({
                'type': 'completed',
                'id': order_key,
                'date': order_date.date().isoformat(),
                'day': day_name
            })

            # Create confirmed order for some days
            if i < 3:
                confirmed_order = {
                    'order_id': f'weekly_test_confirmed_{i}',
                    'order_date': order_date.isoformat(),
                    'customer_id': f'weekly_test_customer_conf_{i}',
                    'status': 'confirmed',
                    'restaurant_name': 'Weekly Test Restaurant',
                    'delivery_address': f'Weekly Test Address Conf {i}',
                    'created_by': 'weekly_test_api',
                    'created_at': datetime.now().isoformat()
                }

                conf_key = f"weekly_test_confirmed_{i}"
                set_data(f"confirmed_orders/{conf_key}", confirmed_order)
                created_orders.append({
                    'type': 'confirmed',
                    'id': conf_key,
                    'date': order_date.date().isoformat(),
                    'day': day_name
                })

        # Create today's specific order
        today_order = {
            'order_id': 'weekly_test_today',
            'completion_date': now.isoformat(),
            'order_date': now.isoformat(),
            'delivery_fee': 100.0,
            'customer_id': 'weekly_test_today_customer',
            'status': 'completed',
            'restaurant_name': 'Today Test Restaurant',
            'delivery_address': 'Today Test Address',
            'created_by': 'weekly_test_api',
            'created_at': now.isoformat()
        }

        set_data("completed_orders/weekly_test_today", today_order)
        created_orders.append({
            'type': 'completed',
            'id': 'weekly_test_today',
            'date': now.date().isoformat(),
            'day': 'TODAY'
        })

        # Test weekly calculation immediately
        stats, daily_breakdown = calculate_accurate_analytics_for_period('weekly')

        result = {
            'success': True,
            'message': f'Created {len(created_orders)} test orders for current week',
            'week_range': {
                'start': monday_this_week.date().isoformat(),
                'end': now.date().isoformat()
            },
            'created_orders': created_orders,
            'immediate_test_results': {
                'total_orders': stats.get('total_orders', 0),
                'completed_orders': stats.get('completed_orders', 0),
                'in_progress_orders': stats.get('in_progress_orders', 0),
                'total_earnings': stats.get('total_earnings', 0),
                'date_range': f"{stats.get('start_date')} to {stats.get('end_date')}"
            },
            'next_steps': [
                'Refresh the analytics page',
                'Weekly period should now show non-zero values',
                'Check the weekly analytics dashboard'
            ]
        }

        logger.info(f"Created weekly test data: {len(created_orders)} orders")
        logger.info(f"Immediate test results: {result['immediate_test_results']}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error creating weekly test data: {e}")
        import traceback
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/debug-dashboard')
@login_required
def debug_dashboard():
    """Comprehensive debug dashboard for analytics and system testing"""
    try:
        from datetime import datetime, timedelta

        # Get current system info
        now = datetime.now()
        current_weekday = now.weekday()
        monday_this_week = (now - timedelta(days=current_weekday)).date()

        # Test Firebase connection
        firebase_status = "Unknown"
        firebase_error = None
        try:
            if initialize_firebase():
                firebase_status = "Connected"
            else:
                firebase_status = "Failed"
        except Exception as e:
            firebase_status = "Error"
            firebase_error = str(e)

        # Get Firebase data counts
        firebase_counts = {}
        try:
            firebase_data = get_real_time_firebase_data()
            if firebase_data:
                firebase_counts = {
                    'delivery_personnel': len(firebase_data.get('delivery_personnel', {})),
                    'completed_orders': len(firebase_data.get('completed_orders', {})),
                    'confirmed_orders': len(firebase_data.get('confirmed_orders', {})),
                    'pending_admin_reviews': len(firebase_data.get('pending_admin_reviews', {})),
                    'current_orders': len(firebase_data.get('current_orders', {}))
                }
        except Exception as e:
            firebase_counts = {'error': str(e)}

        # Test all analytics periods
        analytics_tests = {}
        for period in ['daily', 'weekly', 'monthly', 'all_time']:
            try:
                stats, breakdown = calculate_accurate_analytics_for_period(period)
                analytics_tests[period] = {
                    'status': 'success',
                    'total_orders': stats.get('total_orders', 0),
                    'completed_orders': stats.get('completed_orders', 0),
                    'total_earnings': stats.get('total_earnings', 0),
                    'date_range': f"{stats.get('start_date', 'N/A')} to {stats.get('end_date', 'N/A')}",
                    'breakdown_days': len(breakdown) if breakdown else 0
                }
            except Exception as e:
                analytics_tests[period] = {
                    'status': 'error',
                    'error': str(e)
                }

        debug_info = {
            'timestamp': now.isoformat(),
            'system_info': {
                'current_date': now.date().isoformat(),
                'current_time': now.time().isoformat(),
                'current_weekday': current_weekday,
                'current_weekday_name': now.strftime('%A'),
                'monday_this_week': monday_this_week.isoformat(),
                'week_range': f"{monday_this_week} to {now.date()}"
            },
            'firebase_status': {
                'connection': firebase_status,
                'error': firebase_error,
                'data_counts': firebase_counts
            },
            'analytics_tests': analytics_tests
        }

        return render_template('debug_dashboard.html', debug_info=debug_info)

    except Exception as e:
        logger.error(f"Error in debug dashboard: {e}")
        import traceback
        error_info = {
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        return render_template('debug_dashboard.html', debug_info=None, error_info=error_info)

@app.route('/api/debug/test-all-endpoints')
@login_required
def api_test_all_endpoints():
    """Test all debug API endpoints"""
    try:
        endpoints_to_test = [
            '/api/analytics/debug?period=weekly',
            '/api/analytics/weekly-sync-test',
            '/api/analytics/week-info',
            '/api/analytics/create-weekly-test-data'
        ]

        results = {}

        for endpoint in endpoints_to_test:
            try:
                # We can't easily test these internally, so we'll just check if the routes exist
                endpoint_name = endpoint.split('/')[-1].split('?')[0]
                results[endpoint] = {
                    'status': 'available',
                    'url': f"http://localhost:5000{endpoint}",
                    'description': f"Endpoint {endpoint_name} is defined and should be accessible"
                }
            except Exception as e:
                results[endpoint] = {
                    'status': 'error',
                    'error': str(e)
                }

        return jsonify({
            'test_timestamp': datetime.now().isoformat(),
            'endpoints_tested': len(endpoints_to_test),
            'results': results,
            'instructions': [
                'Click on each URL to test the endpoint manually',
                'All endpoints should return JSON data',
                'Check browser console for any JavaScript errors'
            ]
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/debug/run-diagnostics')
@login_required
def api_run_diagnostics():
    """Run diagnostic functions that were in the Python scripts"""
    try:
        from datetime import datetime, timedelta

        results = {
            'timestamp': datetime.now().isoformat(),
            'diagnostics': {}
        }

        # 1. Analyze existing Firebase data
        try:
            completed_orders = get_data("completed_orders") or {}
            confirmed_orders = get_data("confirmed_orders") or {}

            # Analyze dates in completed orders
            all_dates = []
            date_analysis = []

            for order_id, order_data in completed_orders.items():
                order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')

                analysis_entry = {
                    'order_id': order_id,
                    'raw_date': order_date_str,
                    'delivery_fee': order_data.get('delivery_fee', 'N/A'),
                    'all_keys': list(order_data.keys())
                }

                if order_date_str:
                    try:
                        if 'T' in order_date_str:
                            order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                        else:
                            order_date = datetime.fromisoformat(order_date_str).date()

                        all_dates.append(order_date)
                        analysis_entry['parsed_date'] = order_date.isoformat()
                        analysis_entry['parse_status'] = 'success'
                    except Exception as e:
                        analysis_entry['parse_status'] = 'failed'
                        analysis_entry['parse_error'] = str(e)
                else:
                    analysis_entry['parse_status'] = 'no_date'

                date_analysis.append(analysis_entry)

            # Current week analysis
            now = datetime.now()
            current_weekday = now.weekday()
            monday_this_week = (now - timedelta(days=current_weekday)).date()

            current_week_orders = []
            for order_date in all_dates:
                if monday_this_week <= order_date <= now.date():
                    current_week_orders.append(order_date.isoformat())

            results['diagnostics']['firebase_analysis'] = {
                'status': 'success',
                'completed_orders_count': len(completed_orders),
                'confirmed_orders_count': len(confirmed_orders),
                'orders_with_dates': len(all_dates),
                'date_range_in_data': {
                    'earliest': min(all_dates).isoformat() if all_dates else None,
                    'latest': max(all_dates).isoformat() if all_dates else None,
                    'unique_dates': sorted(list(set(date.isoformat() for date in all_dates)))
                },
                'current_week_analysis': {
                    'monday_this_week': monday_this_week.isoformat(),
                    'week_range': f"{monday_this_week} to {now.date()}",
                    'orders_in_current_week': len(current_week_orders),
                    'current_week_dates': sorted(current_week_orders)
                },
                'detailed_order_analysis': date_analysis[:10]  # First 10 orders
            }

        except Exception as e:
            results['diagnostics']['firebase_analysis'] = {
                'status': 'error',
                'error': str(e)
            }

        # 2. Test weekly calculation
        try:
            stats, daily_breakdown = calculate_accurate_analytics_for_period('weekly')

            results['diagnostics']['weekly_calculation'] = {
                'status': 'success',
                'results': stats,
                'daily_breakdown_days': len(daily_breakdown) if daily_breakdown else 0,
                'daily_breakdown_sample': dict(list(daily_breakdown.items())[:5]) if daily_breakdown else {}
            }

        except Exception as e:
            results['diagnostics']['weekly_calculation'] = {
                'status': 'error',
                'error': str(e)
            }

        # 3. Test all periods consistency
        try:
            period_results = {}
            for period in ['daily', 'weekly', 'monthly', 'all_time']:
                stats, _ = calculate_accurate_analytics_for_period(period)
                period_results[period] = {
                    'total_orders': stats.get('total_orders', 0),
                    'completed_orders': stats.get('completed_orders', 0),
                    'total_earnings': stats.get('total_earnings', 0)
                }

            # Check logical consistency
            consistency_checks = {
                'weekly_le_monthly': period_results['weekly']['total_orders'] <= period_results['monthly']['total_orders'],
                'monthly_le_all_time': period_results['monthly']['total_orders'] <= period_results['all_time']['total_orders'],
                'daily_le_weekly': period_results['daily']['total_orders'] <= period_results['weekly']['total_orders']
            }

            results['diagnostics']['period_consistency'] = {
                'status': 'success',
                'period_results': period_results,
                'consistency_checks': consistency_checks,
                'all_consistent': all(consistency_checks.values())
            }

        except Exception as e:
            results['diagnostics']['period_consistency'] = {
                'status': 'error',
                'error': str(e)
            }

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error running diagnostics: {e}")
        import traceback
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/personnel/refresh')
@login_required
def api_personnel_refresh():
    """Get real-time personnel data for immediate updates"""
    try:
        logger.info("API request for real-time personnel data")

        # Force fresh data from Firebase
        personnel_data = get_authorized_delivery_personnel()

        # Clear authorization cache to ensure fresh authorization data
        try:
            from src.bots.delivery_bot import clear_authorization_cache
            clear_authorization_cache()
        except Exception as cache_error:
            logger.warning(f"Failed to clear authorization cache: {cache_error}")

        # Get authorization status for each personnel
        personnel_with_auth = {}
        for telegram_id, person_data in personnel_data.items():
            try:
                from src.bots.delivery_bot import is_authorized
                auth_status = is_authorized(int(telegram_id))
                person_data['is_authorized'] = auth_status
                person_data['auth_check_time'] = datetime.utcnow().isoformat()
            except Exception as auth_error:
                person_data['is_authorized'] = False
                person_data['auth_error'] = str(auth_error)

            personnel_with_auth[telegram_id] = person_data

        result = {
            'success': True,
            'timestamp': datetime.utcnow().isoformat(),
            'total_personnel': len(personnel_with_auth),
            'personnel': personnel_with_auth,
            'active_count': len([p for p in personnel_with_auth.values() if p.get('status') == 'active']),
            'authorized_count': len([p for p in personnel_with_auth.values() if p.get('is_authorized', False)])
        }

        logger.info(f"Returned real-time data for {len(personnel_with_auth)} personnel")
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error getting real-time personnel data: {e}")
        import traceback
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/personnel/test-authorization/<telegram_id>')
@login_required
def api_test_authorization(telegram_id):
    """Test authorization for a specific personnel"""
    try:
        logger.info(f"Testing authorization for Telegram ID: {telegram_id}")

        # Clear cache first
        try:
            from src.bots.delivery_bot import clear_authorization_cache
            clear_authorization_cache()
        except Exception as cache_error:
            logger.warning(f"Failed to clear authorization cache: {cache_error}")

        # Test authorization
        from src.bots.delivery_bot import is_authorized, get_authorized_delivery_ids_from_firebase

        auth_result = is_authorized(int(telegram_id))
        authorized_ids = get_authorized_delivery_ids_from_firebase()

        # Get personnel data
        personnel_data = get_data(f"delivery_personnel")
        personnel_found = None
        for personnel_id, data in (personnel_data or {}).items():
            if str(data.get('telegram_id')) == str(telegram_id):
                personnel_found = data
                break

        result = {
            'telegram_id': telegram_id,
            'is_authorized': auth_result,
            'authorized_ids_list': authorized_ids,
            'in_authorized_list': int(telegram_id) in authorized_ids,
            'personnel_data': personnel_found,
            'test_timestamp': datetime.utcnow().isoformat()
        }

        logger.info(f"Authorization test result for {telegram_id}: {auth_result}")
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error testing authorization for {telegram_id}: {e}")
        import traceback
        return jsonify({
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

# ============================================================================
# EARNINGS ROUTES
# ============================================================================

@app.route('/earnings')
@login_required
def earnings():
    """Earnings and payroll page"""
    try:
        earnings_data = get_data("delivery_personnel_earnings") or {}
        personnel_data = get_data("delivery_personnel") or {}

        # Combine earnings with personnel info
        combined_data = {}
        for personnel_id, earnings in earnings_data.items():
            personnel_info = personnel_data.get(personnel_id, {})
            combined_data[personnel_id] = {
                'name': personnel_info.get('name', 'Unknown'),
                'telegram_id': personnel_info.get('telegram_id', 'Unknown'),
                'total_earnings': earnings.get('total_earnings', 0),
                'weekly_earnings': earnings.get('weekly_earnings', 0),
                'deliveries_completed': earnings.get('deliveries_completed', 0),
                'status': personnel_info.get('status', 'unknown')
            }

        return render_template('earnings.html', earnings=combined_data)

    except Exception as e:
        logger.error(f"Error loading earnings: {e}")
        flash('Error loading earnings data', 'error')
        return render_template('earnings.html', earnings={})

# ============================================================================
# SYSTEM MANAGEMENT ROUTES
# ============================================================================

@app.route('/system')
@login_required
def system():
    """System management page"""
    try:
        # Get system statistics
        system_stats = {
            'firebase_status': 'Connected',
            'total_collections': 0,
            'last_backup': 'Not implemented',
            'system_uptime': 'Active'
        }

        return render_template('system.html', stats=system_stats)

    except Exception as e:
        logger.error(f"Error loading system page: {e}")
        flash('Error loading system data', 'error')
        return render_template('system.html', stats={})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

    # Set up logging for debugging
    import logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Test authentication setup
    print("🔐 Testing authentication setup...")
    try:
        from werkzeug.security import check_password_hash
        test_hash = DEFAULT_ADMIN_USERS['admin']['password_hash']
        test_result = check_password_hash(test_hash, 'admin123')
        print(f"✅ Password hash test: {'PASSED' if test_result else 'FAILED'}")
    except Exception as e:
        print(f"❌ Password hash test error: {e}")

    print("🚀 Starting Wiz-Aroma Web Management Interface...")
    print("📍 Access URL: http://localhost:5000")
    print("🔑 Default credentials: admin / admin123")

    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
