{% extends "base.html" %}

{% block title %}Dashboard - Wiz-Aroma Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2 mb-0">
        <i class="bi bi-speedometer2"></i> Management Dashboard
    </h1>
    <div class="text-muted">
        <i class="bi bi-clock"></i> Last updated: <span id="lastUpdated"></span>
    </div>
</div>

<!-- Daily Statistics Header -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="bi bi-calendar-day me-2"></i>
        <strong id="dashboardDateHeader">Daily Dashboard - {{ current_date }}</strong>
        <span class="ms-auto">
            <small>
                <i class="bi bi-database"></i> {{ stats.data_source or 'Firebase Firestore' }}
                <br>
                <i class="bi bi-clock"></i> Last updated: {{ stats.last_updated or 'Unknown' }}
            </small>
        </span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <h3>{{ stats.total_personnel or 0 }}</h3>
            <p><i class="bi bi-people"></i> Total Personnel</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #2196F3, #1976D2);">
            <h3>{{ stats.active_personnel or 0 }}</h3>
            <p><i class="bi bi-person-check"></i> Active Personnel</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #FF9800, #F57C00);">
            <h3>{{ stats.total_daily_orders or 0 }}</h3>
            <p><i class="bi bi-box-seam"></i> Today's Orders</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #9C27B0, #7B1FA2);">
            <h3>{{ stats.completed_today or 0 }}</h3>
            <p><i class="bi bi-check-circle"></i> Completed Today</p>
        </div>
    </div>
</div>

<!-- Additional Daily Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>{{ stats.in_progress_today or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-arrow-clockwise"></i> In Progress (Incomplete)</p>
                <small>Orders being processed</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h3>{{ stats.pending_today or 0 }}</h3>
                <p class="mb-0"><i class="bi bi-hourglass-split"></i> Pending Today</p>
                <small>Awaiting assignment</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ stats.completion_rate or 0 }}%</h3>
                <p class="mb-0"><i class="bi bi-graph-up"></i> Completion Rate</p>
                <small>Today's efficiency</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card" style="background: linear-gradient(135deg, #FF9800, #F57C00); color: white;">
            <div class="card-body text-center">
                <h3>{{ "%.2f"|format(stats.total_earnings_today or 0) }}</h3>
                <p class="mb-0"><i class="bi bi-currency-dollar"></i> Earnings Today</p>
                <small>Birr (50% share)</small>
            </div>
        </div>
    </div>
</div>

<!-- Firebase Connection Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-success d-flex align-items-center" id="firebaseStatus">
            <div class="spinner-border spinner-border-sm me-2" role="status" id="firebaseSpinner"
                style="display: none;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <i class="bi bi-database-check me-2" id="firebaseIcon"></i>
            <span id="firebaseStatusText">Firebase Connected - Real-time data active</span>
            <button type="button" class="btn btn-sm btn-outline-success ms-auto" onclick="refreshDashboard()">
                <i class="bi bi-arrow-clockwise"></i> Refresh Data
            </button>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('personnel') }}" class="btn btn-primary w-100">
                            <i class="bi bi-person-plus"></i> Add Personnel
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('analytics') }}" class="btn btn-success w-100">
                            <i class="bi bi-graph-up"></i> View Analytics
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('orders') }}" class="btn btn-warning w-100">
                            <i class="bi bi-box-seam"></i> Manage Orders
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('earnings') }}" class="btn btn-info w-100">
                            <i class="bi bi-currency-dollar"></i> View Earnings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Orders Chart -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-bar-chart"></i> Orders Overview</h5>
            </div>
            <div class="card-body">
                <canvas id="ordersChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Personnel Status Chart -->
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pie-chart"></i> Personnel Status</h5>
            </div>
            <div class="card-body">
                <canvas id="personnelChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-8 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history"></i> Recent Activity</h5>
            </div>
            <div class="card-body">
                <div id="recentActivity">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-success rounded-circle p-2 me-3">
                            <i class="bi bi-check text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">System Initialized</div>
                            <small class="text-muted">Web management interface is ready</small>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-primary rounded-circle p-2 me-3">
                            <i class="bi bi-person text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">Admin Login</div>
                            <small class="text-muted">{{ user.username }} logged in successfully</small>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="bg-info rounded-circle p-2 me-3">
                            <i class="bi bi-database text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">Firebase Connected</div>
                            <small class="text-muted">Database connection established</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check"></i> System Status</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Web Interface</span>
                    <span class="badge bg-success">Online</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Firebase Database</span>
                    <span class="badge bg-success">Connected</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Delivery Bot</span>
                    <span class="badge bg-warning">External</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Order Tracking</span>
                    <span class="badge bg-warning">External</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Dashboard refresh and Firebase monitoring
    let refreshInterval;
    let lastRefreshTime = new Date();

    // Initialize dashboard when page loads
    document.addEventListener('DOMContentLoaded', function () {
        initializeCharts();
        startAutoRefresh();
        updateFirebaseStatus('connected');
    });

    function initializeCharts() {
        // Orders Chart
        const ordersCtx = document.getElementById('ordersChart').getContext('2d');
        new Chart(ordersCtx, {
            type: 'doughnut',
            data: {
                labels: ['Completed', 'In Progress', 'Pending'],
                datasets: [{
                    data: [{{ stats.completed_today or 0 }}, {{ stats.in_progress_today or 0 }}, {{ stats.pending_today or 0 }}],
    backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
        borderWidth: 0
            }]
        },
    options: {
        responsive: true,
            maintainAspectRatio: false,
                plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
    });

    // Personnel Chart
    const personnelCtx = document.getElementById('personnelChart').getContext('2d');
    new Chart(personnelCtx, {
        type: 'bar',
        data: {
            labels: ['Total Personnel', 'Active Personnel'],
            datasets: [{
                label: 'Count',
                data: [{{ stats.total_personnel or 0 }}, {{ stats.active_personnel or 0 }}],
        backgroundColor: [
            '#4CAF50',
            '#2196F3'
        ],
        borderWidth: 0,
        borderRadius: 8
    }]
    },
        options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

}

    function startAutoRefresh() {
        // Auto-refresh every 2 minutes
        refreshInterval = setInterval(refreshDashboard, 120000);
    }

    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    function refreshDashboard() {
        updateFirebaseStatus('refreshing');

        // Show loading state
        const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
        if (refreshBtn) {
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Refreshing...';
            refreshBtn.disabled = true;
        }

        // Reload the page to get fresh data
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    function updateFirebaseStatus(status) {
        const statusElement = document.getElementById('firebaseStatus');
        const iconElement = document.getElementById('firebaseIcon');
        const textElement = document.getElementById('firebaseStatusText');
        const spinnerElement = document.getElementById('firebaseSpinner');

        if (!statusElement) return; // Element not found

        // Remove existing classes
        statusElement.className = 'alert d-flex align-items-center';

        switch (status) {
            case 'connected':
                statusElement.classList.add('alert-success');
                if (iconElement) iconElement.className = 'bi bi-database-check me-2';
                if (textElement) textElement.textContent = 'Firebase Connected - Real-time data active';
                if (spinnerElement) spinnerElement.style.display = 'none';
                break;
            case 'refreshing':
                statusElement.classList.add('alert-info');
                if (iconElement) iconElement.className = 'bi bi-database me-2';
                if (textElement) textElement.textContent = 'Refreshing data from Firebase...';
                if (spinnerElement) spinnerElement.style.display = 'inline-block';
                break;
            case 'error':
                statusElement.classList.add('alert-danger');
                if (iconElement) iconElement.className = 'bi bi-database-x me-2';
                if (textElement) textElement.textContent = 'Firebase connection error - Using cached data';
                if (spinnerElement) spinnerElement.style.display = 'none';
                break;
            default:
                statusElement.classList.add('alert-warning');
                if (iconElement) iconElement.className = 'bi bi-database-exclamation me-2';
                if (textElement) textElement.textContent = 'Firebase status unknown';
                if (spinnerElement) spinnerElement.style.display = 'none';
        }

        lastRefreshTime = new Date();
    }

    // Check for data freshness
    function checkDataFreshness() {
        const dataSource = '{{ stats.data_source or "" }}';
        if (dataSource.includes('Error') || dataSource.includes('fallback')) {
            updateFirebaseStatus('error');
        } else {
            updateFirebaseStatus('connected');
        }
    }

    // Run data freshness check
    checkDataFreshness();
</script>
{% endblock %}