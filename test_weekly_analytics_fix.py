#!/usr/bin/env python
"""
Test script to debug and fix weekly analytics data synchronization issues
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, delete_data, initialize_firebase
from src.config import logger

def create_weekly_test_data():
    """Create test data spanning the last 7 days"""
    print("📅 Creating Weekly Test Data...")
    
    try:
        now = datetime.now()
        
        # Create orders for each day of the last 7 days
        weekly_orders = []
        for i in range(7):  # 0 to 6 days ago
            order_date = now - timedelta(days=i)
            
            # Create completed order
            completed_order = {
                'order_id': f'weekly_completed_{i}',
                'completion_date': order_date.isoformat(),
                'order_date': order_date.isoformat(),
                'delivery_fee': 50.0 + (i * 10),  # Varying fees
                'customer_id': f'weekly_customer_{i}',
                'status': 'completed'
            }
            
            set_data(f"completed_orders/weekly_completed_{i}", completed_order)
            weekly_orders.append(completed_order)
            print(f"✅ Created completed order for {order_date.date()}: weekly_completed_{i}")
            
            # Create confirmed order (in-progress) for some days
            if i < 3:  # Only for last 3 days
                confirmed_order = {
                    'order_id': f'weekly_confirmed_{i}',
                    'order_date': order_date.isoformat(),
                    'customer_id': f'weekly_customer_confirmed_{i}',
                    'status': 'confirmed'
                }
                
                set_data(f"confirmed_orders/weekly_confirmed_{i}", confirmed_order)
                weekly_orders.append(confirmed_order)
                print(f"✅ Created confirmed order for {order_date.date()}: weekly_confirmed_{i}")
        
        print(f"✅ Created {len(weekly_orders)} test orders spanning 7 days")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create weekly test data: {e}")
        return False

def test_weekly_date_range_calculation():
    """Test weekly date range calculation"""
    print("\n📊 Testing Weekly Date Range Calculation...")
    
    try:
        from datetime import datetime, timedelta
        
        now = datetime.now()
        
        # Calculate weekly range as done in the function
        start_date = (now - timedelta(days=6)).date()
        end_date = now.date()
        
        print(f"Current datetime: {now}")
        print(f"Weekly range: {start_date} to {end_date}")
        print(f"Days included: {(end_date - start_date).days + 1}")
        
        # Show each day in the range
        current_date = start_date
        day_count = 0
        while current_date <= end_date:
            day_count += 1
            print(f"  Day {day_count}: {current_date}")
            current_date += timedelta(days=1)
        
        if day_count == 7:
            print("✅ Weekly range includes exactly 7 days")
        else:
            print(f"❌ Weekly range includes {day_count} days, should be 7")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Weekly date range test failed: {e}")
        return False

def test_firebase_data_for_weekly():
    """Test Firebase data retrieval for weekly period"""
    print("\n🔥 Testing Firebase Data for Weekly Period...")
    
    try:
        from web_management import get_real_time_firebase_data
        
        firebase_data = get_real_time_firebase_data()
        
        if not firebase_data:
            print("❌ No Firebase data available")
            return False
        
        print("Firebase collections:")
        for collection, data in firebase_data.items():
            print(f"  {collection}: {len(data)} records")
        
        # Analyze dates in completed orders
        print(f"\nAnalyzing completed orders dates:")
        
        now = datetime.now()
        weekly_start = (now - timedelta(days=6)).date()
        weekly_end = now.date()
        
        weekly_orders = []
        all_dates = []
        
        for order_id, order_data in firebase_data['completed_orders'].items():
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
            
            if order_date_str:
                try:
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()
                    
                    all_dates.append(order_date)
                    
                    if weekly_start <= order_date <= weekly_end:
                        weekly_orders.append({
                            'id': order_id,
                            'date': order_date,
                            'fee': order_data.get('delivery_fee', 0)
                        })
                        print(f"  ✅ {order_id}: {order_date} (fee: {order_data.get('delivery_fee', 0)})")
                    else:
                        print(f"  ❌ {order_id}: {order_date} (outside weekly range)")
                        
                except Exception as e:
                    print(f"  ⚠️  {order_id}: Invalid date '{order_date_str}' - {e}")
            else:
                print(f"  ⚠️  {order_id}: No date information")
        
        print(f"\nWeekly analysis:")
        print(f"  Weekly range: {weekly_start} to {weekly_end}")
        print(f"  Orders in weekly range: {len(weekly_orders)}")
        print(f"  Total orders with dates: {len(all_dates)}")
        
        if all_dates:
            print(f"  Date range in data: {min(all_dates)} to {max(all_dates)}")
        
        return len(weekly_orders) > 0
        
    except Exception as e:
        print(f"❌ Firebase data test failed: {e}")
        return False

def test_weekly_analytics_calculation():
    """Test weekly analytics calculation specifically"""
    print("\n🧮 Testing Weekly Analytics Calculation...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Test weekly calculation
        stats, daily_breakdown = calculate_accurate_analytics_for_period('weekly')
        
        print("Weekly analytics results:")
        print(f"  Period: {stats.get('period', 'unknown')}")
        print(f"  Date range: {stats.get('start_date')} to {stats.get('end_date')}")
        print(f"  Total orders: {stats.get('total_orders', 0)}")
        print(f"  Completed orders: {stats.get('completed_orders', 0)}")
        print(f"  In-progress orders: {stats.get('in_progress_orders', 0)}")
        print(f"  Pending orders: {stats.get('pending_orders', 0)}")
        print(f"  Completion rate: {stats.get('completion_rate', 0)}%")
        print(f"  Total earnings: {stats.get('total_earnings', 0):.2f}")
        
        print(f"\nDaily breakdown:")
        print(f"  Days with data: {len(daily_breakdown) if daily_breakdown else 0}")
        
        if daily_breakdown:
            for date, data in sorted(daily_breakdown.items()):
                print(f"    {date}: completed={data.get('completed', 0)}, in_progress={data.get('in_progress', 0)}, pending={data.get('pending', 0)}")
        
        # Compare with all-time to validate
        all_time_stats, _ = calculate_accurate_analytics_for_period('all_time')
        
        print(f"\nComparison with all-time:")
        print(f"  Weekly total orders: {stats.get('total_orders', 0)}")
        print(f"  All-time total orders: {all_time_stats.get('total_orders', 0)}")
        
        if stats.get('total_orders', 0) <= all_time_stats.get('total_orders', 0):
            print("✅ Weekly orders <= All-time orders (logical)")
        else:
            print("❌ Weekly orders > All-time orders (illogical)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Weekly analytics calculation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_weekly_vs_daily_consistency():
    """Test consistency between weekly and daily calculations"""
    print("\n🔄 Testing Weekly vs Daily Consistency...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Get daily and weekly stats
        daily_stats, _ = calculate_accurate_analytics_for_period('daily')
        weekly_stats, _ = calculate_accurate_analytics_for_period('weekly')
        
        print("Consistency check:")
        print(f"  Daily total orders: {daily_stats.get('total_orders', 0)}")
        print(f"  Weekly total orders: {weekly_stats.get('total_orders', 0)}")
        
        # Weekly should include daily (unless daily is 0 and weekly has historical data)
        if weekly_stats.get('total_orders', 0) >= daily_stats.get('total_orders', 0):
            print("✅ Weekly >= Daily orders (logical)")
        else:
            print("❌ Weekly < Daily orders (illogical)")
            return False
        
        print(f"  Daily completed: {daily_stats.get('completed_orders', 0)}")
        print(f"  Weekly completed: {weekly_stats.get('completed_orders', 0)}")
        
        if weekly_stats.get('completed_orders', 0) >= daily_stats.get('completed_orders', 0):
            print("✅ Weekly >= Daily completed orders (logical)")
        else:
            print("❌ Weekly < Daily completed orders (illogical)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Consistency test failed: {e}")
        return False

def test_debug_api_weekly():
    """Test the debug API for weekly period"""
    print("\n🐛 Testing Debug API for Weekly Period...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            with app.test_request_context('/api/analytics/debug?period=weekly'):
                from web_management import api_analytics_debug
                
                try:
                    # Mock the request args
                    import flask
                    flask.request.args = {'period': 'weekly'}
                    
                    result = api_analytics_debug()
                    
                    if hasattr(result, 'get_json'):
                        debug_data = result.get_json()
                        print("Debug API results:")
                        print(f"  Period: {debug_data.get('period', 'unknown')}")
                        print(f"  Current date: {debug_data.get('current_date', 'unknown')}")
                        print(f"  Firebase data available: {debug_data.get('firebase_data_available', False)}")
                        
                        if 'calculated_stats' in debug_data:
                            stats = debug_data['calculated_stats']
                            print(f"  Calculated total orders: {stats.get('total_orders', 0)}")
                            print(f"  Calculated completed orders: {stats.get('completed_orders', 0)}")
                        
                        if 'date_analysis' in debug_data:
                            date_analysis = debug_data['date_analysis']
                            print(f"  Weekly range from debug: {date_analysis.get('period_range', {}).get('weekly', 'unknown')}")
                        
                        print("✅ Debug API working for weekly period")
                    else:
                        print("❌ Debug API returned invalid response")
                        return False
                        
                except Exception as e:
                    print(f"❌ Debug API failed: {e}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Debug API test failed: {e}")
        return False

def main():
    """Run all weekly analytics tests"""
    print("📊 WEEKLY ANALYTICS DATA SYNCHRONIZATION FIX")
    print("=" * 60)
    
    tests = [
        ("Create Weekly Test Data", create_weekly_test_data),
        ("Weekly Date Range Calculation", test_weekly_date_range_calculation),
        ("Firebase Data for Weekly", test_firebase_data_for_weekly),
        ("Weekly Analytics Calculation", test_weekly_analytics_calculation),
        ("Weekly vs Daily Consistency", test_weekly_vs_daily_consistency),
        ("Debug API Weekly", test_debug_api_weekly)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL WEEKLY ANALYTICS TESTS PASSED!")
        print("\n✅ Weekly Analytics Should Now Show:")
        print("• Accurate data from last 7 days including today")
        print("• Real-time synchronization with Firebase")
        print("• Proper date filtering and calculation")
        print("• Consistent data across all time periods")
        print("\n🚀 Next Steps:")
        print("1. Restart web interface: python web_management.py")
        print("2. Test weekly analytics: http://localhost:5000/analytics?period=weekly")
        print("3. Debug if needed: http://localhost:5000/api/analytics/debug?period=weekly")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("❌ Weekly analytics synchronization needs attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}: Weekly analytics {'fixed' if success else 'needs work'}")
    sys.exit(0 if success else 1)
