<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Wiz-Aroma Management</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --wiz-primary: #4CAF50;
            --wiz-secondary: #2E7D32;
            --wiz-accent: #FFC107;
            --wiz-dark: #1B5E20;
            --wiz-light: #E8F5E8;
        }
        
        body {
            background: linear-gradient(135deg, var(--wiz-primary), var(--wiz-secondary));
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-left {
            background: linear-gradient(135deg, var(--wiz-primary), var(--wiz-secondary));
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .brand-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .feature-list {
            text-align: left;
            max-width: 300px;
            margin: 0 auto;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1rem;
        }
        
        .feature-item i {
            margin-right: 12px;
            font-size: 1.2rem;
            color: var(--wiz-accent);
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .form-title {
            color: var(--wiz-dark);
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--wiz-primary);
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--wiz-primary), var(--wiz-secondary));
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, var(--wiz-secondary), var(--wiz-dark));
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        
        .form-check {
            margin-bottom: 25px;
        }
        
        .form-check-input:checked {
            background-color: var(--wiz-primary);
            border-color: var(--wiz-primary);
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 25px;
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 30px;
            }
            
            .login-right {
                padding: 40px 30px;
            }
            
            .brand-title {
                font-size: 1.5rem;
            }
            
            .form-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="login-container">
                    <div class="row g-0">
                        <!-- Left Side - Branding -->
                        <div class="col-md-6 login-left">
                            <div class="logo">
                                <i class="bi bi-shop"></i>
                            </div>
                            <h1 class="brand-title">Wiz-Aroma</h1>
                            <p class="brand-subtitle">Management Dashboard</p>
                            
                            <div class="feature-list">
                                <div class="feature-item">
                                    <i class="bi bi-people"></i>
                                    <span>Personnel Management</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-graph-up"></i>
                                    <span>Analytics & Reports</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-box-seam"></i>
                                    <span>Order Tracking</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-currency-dollar"></i>
                                    <span>Earnings Management</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-shield-check"></i>
                                    <span>Secure Access</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right Side - Login Form -->
                        <div class="col-md-6 login-right">
                            <div class="login-form">
                                <h2 class="form-title">Welcome Back</h2>
                                
                                <!-- Flash Messages -->
                                {% with messages = get_flashed_messages(with_categories=true) %}
                                    {% if messages %}
                                        {% for category, message in messages %}
                                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                                {{ message }}
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                        {% endfor %}
                                    {% endif %}
                                {% endwith %}
                                
                                <form method="POST">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="username" name="username" 
                                               placeholder="Username" required>
                                        <label for="username">
                                            <i class="bi bi-person"></i> Username
                                        </label>
                                    </div>
                                    
                                    <div class="form-floating">
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="Password" required>
                                        <label for="password">
                                            <i class="bi bi-lock"></i> Password
                                        </label>
                                    </div>
                                    
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">
                                            Remember me for 8 hours
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-login">
                                        <i class="bi bi-box-arrow-in-right"></i> Sign In
                                    </button>
                                </form>
                                
                                <div class="text-center mt-4">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i> 
                                        Default credentials: admin / admin123
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
