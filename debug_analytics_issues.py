#!/usr/bin/env python
"""
Debug script to identify and fix analytics data display issues
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data, delete_data, initialize_firebase
from src.config import logger

def create_test_data():
    """Create test data for debugging"""
    print("🔧 Creating Test Data...")
    
    try:
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        week_ago = today - timedelta(days=7)
        
        # Create test completed orders with different dates
        test_orders = [
            {
                'id': 'test_today_001',
                'completion_date': today.isoformat(),
                'order_date': today.isoformat(),
                'delivery_fee': 50.0,
                'customer_id': 'test_customer_1',
                'status': 'completed'
            },
            {
                'id': 'test_yesterday_001',
                'completion_date': yesterday.isoformat(),
                'order_date': yesterday.isoformat(),
                'delivery_fee': 75.0,
                'customer_id': 'test_customer_2',
                'status': 'completed'
            },
            {
                'id': 'test_week_ago_001',
                'completion_date': week_ago.isoformat(),
                'order_date': week_ago.isoformat(),
                'delivery_fee': 60.0,
                'customer_id': 'test_customer_3',
                'status': 'completed'
            }
        ]
        
        # Save test orders
        for order in test_orders:
            set_data(f"completed_orders/{order['id']}", order)
            print(f"✅ Created test order: {order['id']} ({order['completion_date'][:10]})")
        
        # Create test confirmed orders
        test_confirmed = [
            {
                'id': 'test_confirmed_001',
                'order_date': today.isoformat(),
                'customer_id': 'test_customer_4',
                'status': 'confirmed'
            }
        ]
        
        for order in test_confirmed:
            set_data(f"confirmed_orders/{order['id']}", order)
            print(f"✅ Created test confirmed order: {order['id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test data: {e}")
        return False

def test_date_filtering_logic():
    """Test the date filtering logic directly"""
    print("\n📅 Testing Date Filtering Logic...")
    
    try:
        from datetime import datetime, timedelta
        
        # Test current date logic
        now = datetime.now()
        
        # Test daily period
        daily_start = now.date()
        daily_end = now.date()
        print(f"Daily period: {daily_start} to {daily_end}")
        
        # Test weekly period  
        weekly_start = (now - timedelta(days=6)).date()
        weekly_end = now.date()
        print(f"Weekly period: {weekly_start} to {weekly_end}")
        
        # Test monthly period
        monthly_start = (now - timedelta(days=29)).date()
        monthly_end = now.date()
        print(f"Monthly period: {monthly_start} to {monthly_end}")
        
        # Test with sample dates
        test_dates = [
            now.date(),  # Today
            (now - timedelta(days=1)).date(),  # Yesterday
            (now - timedelta(days=3)).date(),  # 3 days ago
            (now - timedelta(days=10)).date(),  # 10 days ago
            (now - timedelta(days=35)).date(),  # 35 days ago
        ]
        
        print("\nDate filtering test:")
        for test_date in test_dates:
            daily_match = daily_start <= test_date <= daily_end
            weekly_match = weekly_start <= test_date <= weekly_end
            monthly_match = monthly_start <= test_date <= monthly_end
            
            print(f"Date {test_date}: Daily={daily_match}, Weekly={weekly_match}, Monthly={monthly_match}")
        
        return True
        
    except Exception as e:
        print(f"❌ Date filtering test failed: {e}")
        return False

def test_analytics_calculation_step_by_step():
    """Test analytics calculation step by step"""
    print("\n🔍 Testing Analytics Calculation Step by Step...")
    
    try:
        from web_management import get_real_time_firebase_data
        
        # Get Firebase data
        firebase_data = get_real_time_firebase_data()
        
        if not firebase_data:
            print("❌ No Firebase data available")
            return False
        
        print(f"Firebase collections:")
        for collection, data in firebase_data.items():
            print(f"  {collection}: {len(data)} records")
        
        # Test for daily period
        from datetime import datetime, timedelta
        
        now = datetime.now()
        start_date = now.date()
        end_date = now.date()
        
        print(f"\nTesting daily period: {start_date} to {end_date}")
        print(f"Current datetime: {now}")
        
        # Manually process completed orders
        completed_count = 0
        total_earnings = 0.0
        
        print(f"\nProcessing {len(firebase_data['completed_orders'])} completed orders:")
        
        for order_id, order_data in firebase_data['completed_orders'].items():
            order_date_str = order_data.get('completion_date') or order_data.get('order_date', '')
            print(f"\nOrder {order_id}:")
            print(f"  Raw date string: '{order_date_str}'")
            
            if order_date_str:
                try:
                    # Parse date string
                    if 'T' in order_date_str:
                        order_date = datetime.fromisoformat(order_date_str.replace('Z', '+00:00')).date()
                    else:
                        order_date = datetime.fromisoformat(order_date_str).date()
                    
                    print(f"  Parsed date: {order_date}")
                    print(f"  Date comparison: {start_date} <= {order_date} <= {end_date}")
                    
                    # Check if order falls within the period
                    if start_date <= order_date <= end_date:
                        completed_count += 1
                        
                        # Calculate earnings
                        delivery_fee = order_data.get('delivery_fee', 0)
                        if isinstance(delivery_fee, (int, float)):
                            total_earnings += delivery_fee * 0.5
                        
                        print(f"  ✅ INCLUDED: fee={delivery_fee}, earnings_share={delivery_fee * 0.5 if isinstance(delivery_fee, (int, float)) else 0}")
                    else:
                        print(f"  ❌ EXCLUDED: outside date range")
                        
                except Exception as e:
                    print(f"  ❌ Date parsing failed: {e}")
            else:
                print(f"  ⚠️  No date information")
        
        print(f"\nManual calculation results:")
        print(f"  Completed orders (daily): {completed_count}")
        print(f"  Total earnings (daily): {total_earnings:.2f}")
        
        # Now test the actual function
        from web_management import calculate_accurate_analytics_for_period
        
        stats, daily_breakdown = calculate_accurate_analytics_for_period('daily')
        
        print(f"\nFunction calculation results:")
        print(f"  Completed orders (daily): {stats.get('completed_orders', 0)}")
        print(f"  Total earnings (daily): {stats.get('total_earnings', 0):.2f}")
        print(f"  Total orders (daily): {stats.get('total_orders', 0)}")
        
        # Compare results
        if completed_count == stats.get('completed_orders', 0):
            print("✅ Manual and function calculations match!")
        else:
            print("❌ Manual and function calculations don't match!")
        
        return True
        
    except Exception as e:
        print(f"❌ Step-by-step test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_chart_data_generation():
    """Test chart data generation"""
    print("\n📊 Testing Chart Data Generation...")
    
    try:
        from web_management import calculate_accurate_analytics_for_period
        
        # Test all periods
        periods = ['daily', 'weekly', 'monthly', 'all_time']
        
        for period in periods:
            print(f"\n--- {period.upper()} PERIOD ---")
            stats, daily_breakdown = calculate_accurate_analytics_for_period(period)
            
            print(f"Stats: {stats}")
            print(f"Daily breakdown: {daily_breakdown}")
            
            if daily_breakdown:
                print(f"Daily breakdown has {len(daily_breakdown)} days")
                for date, data in list(daily_breakdown.items())[:3]:  # Show first 3 days
                    print(f"  {date}: {data}")
            else:
                print("❌ No daily breakdown data generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Chart data test failed: {e}")
        return False

def test_template_data_flow():
    """Test data flow to template"""
    print("\n🌐 Testing Template Data Flow...")
    
    try:
        from web_management import app
        
        with app.test_client() as client:
            with app.test_request_context('/analytics?period=daily'):
                from web_management import analytics
                
                # Test analytics route
                try:
                    response = analytics()
                    print("✅ Analytics route executes without errors")
                    
                    # Check if response contains data
                    if hasattr(response, 'data') or isinstance(response, str):
                        print("✅ Analytics route returns valid response")
                    else:
                        print("❌ Analytics route returns invalid response")
                        
                except Exception as e:
                    print(f"❌ Analytics route failed: {e}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Template data flow test failed: {e}")
        return False

def main():
    """Run all debug tests"""
    print("🐛 ANALYTICS DEBUG COMPREHENSIVE TEST")
    print("=" * 60)
    
    tests = [
        ("Create Test Data", create_test_data),
        ("Date Filtering Logic", test_date_filtering_logic),
        ("Analytics Calculation Step by Step", test_analytics_calculation_step_by_step),
        ("Chart Data Generation", test_chart_data_generation),
        ("Template Data Flow", test_template_data_flow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 DEBUG RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL DEBUG TESTS PASSED!")
        print("\n✅ Next Steps:")
        print("1. Restart web interface: python web_management.py")
        print("2. Test analytics: http://localhost:5000/analytics")
        print("3. Check debug endpoint: http://localhost:5000/api/analytics/debug?period=daily")
        print("4. Verify all time periods show data")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("❌ Analytics issues need further investigation")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILURE'}: Analytics debug {'completed' if success else 'needs work'}")
    sys.exit(0 if success else 1)
